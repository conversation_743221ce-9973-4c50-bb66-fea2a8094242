# This file was autogenerated by uv via the following command:
#    uv pip compile scriptdata/requirements.in -o scriptdata/requirements.txt
build==1.2.2.post1
    # via -r scriptdata/requirements.in
cffi==1.17.1
    # via pywayland
libsass==0.23.0
    # via -r scriptdata/requirements.in
lxml==5.3.2
    # via -r scriptdata/requirements.in
material-color-utilities==0.2.1
    # via -r scriptdata/requirements.in
materialyoucolor==2.0.10
    # via -r scriptdata/requirements.in
numpy==2.2.2
    # via material-color-utilities
packaging==24.2
    # via
    #   build
    #   setuptools-scm
pillow==11.1.0
    # via
    #   -r scriptdata/requirements.in
    #   material-color-utilities
psutil==6.1.1
    # via -r scriptdata/requirements.in
pycparser==2.22
    # via cffi
pyproject-hooks==1.2.0
    # via build
# pywal==3.3.0
    # via -r scriptdata/requirements.in
pywayland==0.4.18
    # via -r scriptdata/requirements.in
setproctitle==1.3.4
    # via -r scriptdata/requirements.in
setuptools==75.8.0
    # via setuptools-scm
setuptools-scm==8.1.0
    # via -r scriptdata/requirements.in
wheel==0.45.1
    # via -r scriptdata/requirements.in
