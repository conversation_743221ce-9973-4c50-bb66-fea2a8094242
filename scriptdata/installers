# This file is provided for non-Arch(based) distros.
# As for Arch Linux, we use local PKGBUILDs or AUR packages, which is the "right" way compared to copying files directly into /usr/local/* .
# P.S. install-yay() should be kept for Arch(based) distros.

# This script depends on `functions' .
# This is NOT a script for execution, but for loading functions, so NOT need execution permission or shebang.
# NOTE that you NOT need to `cd ..' because the `$0' is NOT this file, but the script file which will source this file.

# The script that use this file should have two lines on its top as follows:
# cd "$(dirname "$0")"
# export base="$(pwd)"

# Only for Arch(based) distro.
install-yay() {
  x sudo pacman -S --needed --noconfirm base-devel
  x git clone https://aur.archlinux.org/yay-bin.git /tmp/buildyay
  x cd /tmp/buildyay
  x makepkg -o
  x makepkg -se
  x makepkg -i --noconfirm
  x cd $base
  rm -rf /tmp/buildyay
}

# Not for Arch(based) distro.
install-agsv1 (){
  x mkdir -p $base/cache/agsv1
  x cd $base/cache/agsv1
  try git init -b main
  try git remote add origin https://github.com/Aylur/ags.git
  x git pull origin main && git submodule update --init --recursive
  x git fetch --tags
  x git checkout v1.9.0
  x npm install
  x meson setup build # --reconfigure
  x meson install -C build
  x sudo mv /usr/local/bin/ags{,v1}
  x cd $base
}

# Not for Arch(based) distro.
install-Rubik (){
  x mkdir -p $base/cache/Rubik
  x cd $base/cache/Rubik
  try git init -b main
  try git remote add origin https://github.com/googlefonts/rubik.git
  x git pull origin main && git submodule update --init --recursive
	x sudo mkdir -p /usr/local/share/fonts/TTF/
	x sudo cp fonts/variable/Rubik*.ttf /usr/local/share/fonts/TTF/
	x sudo mkdir -p /usr/local/share/licenses/ttf-rubik/
	x sudo cp OFL.txt /usr/local/share/licenses/ttf-rubik/LICENSE
  x fc-cache -fv
  x gsettings set org.gnome.desktop.interface font-name 'Rubik 11'
  x cd $base
}

# Not for Arch(based) distro.
install-Gabarito (){
  x mkdir -p $base/cache/Gabarito
  x cd $base/cache/Gabarito
  try git init -b main
  try git remote add origin https://github.com/naipefoundry/gabarito.git
  x git pull origin main && git submodule update --init --recursive
	x sudo mkdir -p /usr/local/share/fonts/TTF/
	x sudo cp fonts/ttf/Gabarito*.ttf /usr/local/share/fonts/TTF/
	x sudo mkdir -p /usr/local/share/licenses/ttf-gabarito/
	x sudo cp OFL.txt /usr/local/share/licenses/ttf-gabarito/LICENSE
  x fc-cache -fv
  x cd $base
}

# Not for Arch(based) distro.
install-OneUI (){
  x mkdir -p $base/cache/OneUI4-Icons
  x cd $base/cache/OneUI4-Icons
  try git init -b main
  try git remote add origin https://github.com/end-4/OneUI4-Icons.git
# try git remote add origin https://github.com/mjkim0727/OneUI4-Icons.git
  x git pull origin main && git submodule update --init --recursive
  x sudo mkdir -p /usr/local/share/icons
  x sudo cp -r OneUI /usr/local/share/icons
  x sudo cp -r OneUI-dark /usr/local/share/icons
  x sudo cp -r OneUI-light /usr/local/share/icons
  x cd $base
}

# Not for Arch(based) distro.
install-bibata (){
  x mkdir -p $base/cache/bibata-cursor
  x cd $base/cache/bibata-cursor
  name="Bibata-Modern-Classic"
  file="$name.tar.xz"
  # Use axel because `curl -O` always downloads a file with 0 byte size, idk why
  x axel https://github.com/ful1e5/Bibata_Cursor/releases/latest/download/$file
  tar -xf $file
  x sudo mkdir -p /usr/local/share/icons
  x sudo cp -r $name /usr/local/share/icons
  x cd $base
}

# Not for Arch(based) distro.
install-MicroTeX (){
  x mkdir -p $base/cache/MicroTeX
  x cd $base/cache/MicroTeX
  try git init -b master
  try git remote add origin https://github.com/NanoMichael/MicroTeX.git
  x git pull origin master && git submodule update --init --recursive
  x mkdir -p build
  x cd build
  x cmake ..
  x make -j32
	x sudo mkdir -p /opt/MicroTeX
  x sudo cp ./LaTeX /opt/MicroTeX/
  x sudo cp -r ./res /opt/MicroTeX/
  x cd $base
}

# Not for Arch(based) distro.
install-uv (){
  x bash <(curl -LJs "https://astral.sh/uv/install.sh")
}

# Both for Arch(based) and other distros.
install-python-packages (){
  UV_NO_MODIFY_PATH=1
  ILLOGICAL_IMPULSE_VIRTUAL_ENV=$XDG_STATE_HOME/ags/.venv
  x mkdir -p $(eval echo $ILLOGICAL_IMPULSE_VIRTUAL_ENV)
  x uv venv --prompt .venv $(eval echo $ILLOGICAL_IMPULSE_VIRTUAL_ENV) -p 3.13
  x source $(eval echo $ILLOGICAL_IMPULSE_VIRTUAL_ENV)/bin/activate
  x uv pip install -r scriptdata/requirements.txt
  x deactivate # We don't need the virtual environment anymore
}

# Only for Arch(based) distro.
handle-deprecated-dependencies (){
  printf "\e[36m[$0]: Removing deprecated dependencies:\e[0m\n"
  for i in illogical-impulse-{microtex,pymyc-aur,ags,agsv1} {hyprutils,hyprpicker,hyprlang,hypridle,hyprland-qt-support,hyprland-qtutils,hyprlock,xdg-desktop-portal-hyprland,hyprcursor,hyprwayland-scanner,hyprland}-git;do try sudo pacman --noconfirm -Rdd $i;done
# Convert old dependencies to non explicit dependencies so that they can be orphaned if not in meta packages
	remove_bashcomments_emptylines ./scriptdata/previous_dependencies.conf ./cache/old_deps_stripped.conf
	readarray -t old_deps_list < ./cache/old_deps_stripped.conf
	pacman -Qeq > ./cache/pacman_explicit_packages
	readarray -t explicitly_installed < ./cache/pacman_explicit_packages

	echo "Attempting to set previously explicitly installed deps as implicit..."
	for i in "${explicitly_installed[@]}"; do for j in "${old_deps_list[@]}"; do
		[ "$i" = "$j" ] && yay -D --asdeps "$i"
	done; done

	return 0
}
