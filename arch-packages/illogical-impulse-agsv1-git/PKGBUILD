# <AUTHOR> <EMAIL>
pkgname=illogical-impulse-agsv1-git
_pkgname=ii-agsv1
pkgver=r4.3e8d365
pkgrel=4
pkgdesc="Aylurs's Gtk Shell (AGS), patched for illogical-impulse dotfiles."
arch=('x86_64')
url="https://github.com/end-4/ii-agsv1"
license=('GPL-3.0-only')
makedepends=('git' 'gobject-introspection' 'meson' 'npm' 'typescript')
depends=('gvfs' 'gjs' 'glib2' 'glib2-devel' 'glibc' 'gtk3' 'gtk-layer-shell' 'libpulse' 'pam' 'gnome-bluetooth-3.0' 'gammastep')
optdepends=('greetd: required for greetd service'
            'libdbusmenu-gtk3: required for systemtray service'
            'libsoup3: required for the Utils.fetch feature'
            'libnotify: required for sending notifications'
            'networkmanager: required for network service'
            'power-profiles-daemon: required for powerprofiles service'
            'upower: required for battery service')
conflicts=('illogical-impulse-agsv1')
backup=('etc/pam.d/ags')
source=("git+${url}.git")
sha256sums=('SKIP')

pkgver(){
  cd $srcdir/$_pkgname
  printf 'r%s.%s' "$(git rev-list --count HEAD)" "$(git rev-parse --short HEAD)"
}

prepare() {
  cd $srcdir/$_pkgname
}

build() {
  cd $srcdir/$_pkgname
  npm install
  arch-meson build --libdir "lib/$_pkgname" -Dbuild_types=true
  meson compile -C build
}

package() {
  cd $srcdir/$_pkgname
  meson install -C build --destdir "$pkgdir"
  rm ${pkgdir}/usr/bin/ags
  ln -sf /usr/share/com.github.Aylur.ags/com.github.Aylur.ags ${pkgdir}/usr/bin/agsv1
}
