$lock_cmd = pidof hyprlock || hyprlock
$suspend_cmd = systemctl suspend || loginctl suspend

general {
    lock_cmd = $lock_cmd
    before_sleep_cmd = loginctl lock-session
}

listener {
    timeout = 180 # 3mins
    on-timeout = loginctl lock-session
}

listener {
    timeout = 240 # 4mins
    on-timeout = hyprctl dispatch dpms off
    on-resume = hyprctl dispatch dpms on
}

listener {
    timeout = 540 # 9mins
    on-timeout = $suspend_cmd
}
