# Lines ending with `# [hidden]` won't be shown on cheatsheet
# Lines starting with #! are section headings

bindl = Alt ,XF86AudioMute, exec, wpctl set-mute @DEFAULT_SOURCE@ toggle # [hidden]
bindl = Super ,XF86AudioMute, exec, wpctl set-mute @DEFAULT_SOURCE@ toggle # [hidden]
bindl = ,XF86AudioMicMute, exec, wpctl set-mute @DEFAULT_AUDIO_SOURCE@ toggle # [hidden]
bindl = ,XF86AudioMute, exec, wpctl set-mute @DEFAULT_AUDIO_SINK@ toggle # [hidden]
bindl = Super+Shift,M, exec, wpctl set-mute @DEFAULT_AUDIO_SINK@ toggle # [hidden]
bindle=, XF86AudioRaiseVolume, exec, wpctl set-mute @DEFAULT_AUDIO_SINK@ 0 && wpctl set-volume -l 1 @DEFAULT_AUDIO_SINK@ 5%+ # [hidden]
bindle=, XF86AudioLowerVolume, exec, wpctl set-mute @DEFAULT_AUDIO_SINK@ 0 && wpctl set-volume @DEFAULT_AUDIO_SINK@ 5%- # [hidden]

# Uncomment these if you can't get AGS to work
#bindle=, XF86MonBrightnessUp, exec, brightnessctl set '12.75+'
#bindle=, XF86MonBrightnessDown, exec, brightnessctl set '12.75-'

#!
##! Essentials for beginners

bind = Super, T, exec, foot # Launch foot (terminal)
bind = Super, Return, exec, foot # [hidden] # In case you're from i3 or its Wayland clone
bind = , Super, exec, true # Open app launcher
bind = Ctrl+Super, T, exec, ~/.config/ags/scripts/color_generation/switchwall.sh # Change wallpaper
##! Actions
# Screenshot, Record, OCR, Color picker, Clipboard history
bind = Super, V, exec, pkill fuzzel || cliphist list | fuzzel  --match-mode fzf --dmenu | cliphist decode | wl-copy # Clipboard history >> clipboard
bind = Super, Period, exec, pkill fuzzel || ~/.local/bin/fuzzel-emoji # Pick emoji >> clipboard
bind = Ctrl+Shift+Alt, Delete, exec, pkill wlogout || wlogout -p layer-shell # [hidden]
bind = Super+Shift, S, exec, ~/.config/ags/scripts/grimblast.sh --freeze copy area # Screen snip
bind = Super+Shift+Alt, S, exec, grim -g "$(slurp)" - | swappy -f - # Screen snip >> edit
# OCR
bind = Super+Shift,T,exec,grim -g "$(slurp $SLURP_ARGS)" "tmp.png" && tesseract -l eng "tmp.png" - | wl-copy && rm "tmp.png" # Screen snip to text >> clipboard
bind = Ctrl+Super+Shift,S,exec,grim -g "$(slurp $SLURP_ARGS)" "tmp.png" && tesseract "tmp.png" - | wl-copy && rm "tmp.png" # [hidden]
# Color picker
bind = Super+Shift, C, exec, hyprpicker -a # Pick color (Hex) >> clipboard
# Fullscreen screenshot
bindl=,Print,exec,grim - | wl-copy # Screenshot >> clipboard
bindl= Ctrl,Print, exec, mkdir -p ~/Pictures/Screenshots && ~/.config/ags/scripts/grimblast.sh copysave screen ~/Pictures/Screenshots/Screenshot_"$(date '+%Y-%m-%d_%H.%M.%S')".png # Screenshot >> clipboard & file
# AI
bind = Super+Shift+Alt, mouse:273, exec, ~/.config/ags/scripts/ai/primary-buffer-query.sh # Provide AI response for selected text

# Recording stuff
bind = Super+Alt, R, exec, ~/.config/ags/scripts/record-script.sh # Record region (no sound)
bind = Ctrl+Alt, R, exec, ~/.config/ags/scripts/record-script.sh --fullscreen # [hidden] Record screen (no sound)
bind = Super+Shift+Alt, R, exec, ~/.config/ags/scripts/record-script.sh --fullscreen-sound # Record screen (with sound)
##! Session
bind = Ctrl+Super, L, exec, agsv1 run-js 'lock.lock()' # [hidden]
bind = Super, L, exec, loginctl lock-session # Lock
bind = Super+Shift, L, exec, loginctl lock-session # [hidden]
bindl = Super+Shift, L, exec, sleep 0.1 && systemctl suspend || loginctl suspend # Suspend system
bind = Ctrl+Shift+Alt+Super, Delete, exec, systemctl poweroff || loginctl poweroff # [hidden] Power off

#!
##! Window management
# Focusing
#/# bind = Super, ←/↑/→/↓,, # Move focus in direction
bind = Super, Left, movefocus, l # [hidden]
bind = Super, Right, movefocus, r # [hidden]
bind = Super, Up, movefocus, u # [hidden]
bind = Super, Down, movefocus, d # [hidden]
bind = Super, BracketLeft, movefocus, l # [hidden]
bind = Super, BracketRight, movefocus, r # [hidden]
bindm = Super, mouse:272, movewindow
bindm = Super, mouse:273, resizewindow
bind = Super, Q, killactive,
bind = Super+Shift+Alt, Q, exec, hyprctl kill # Pick and kill a window
##! Window arrangement
#/# bind = Super+Shift, ←/↑/→/↓,, # Window: move in direction
bind = Super+Shift, Left, movewindow, l # [hidden]
bind = Super+Shift, Right, movewindow, r # [hidden]
bind = Super+Shift, Up, movewindow, u # [hidden]
bind = Super+Shift, Down, movewindow, d # [hidden]
# Window split ratio
#/# binde = Super, +/-,, # Window: split ratio +/- 0.1
binde = Super, Minus, splitratio, -0.1 # [hidden]
binde = Super, Equal, splitratio, +0.1 # [hidden]
binde = Super, Semicolon, splitratio, -0.1 # [hidden]
binde = Super, Apostrophe, splitratio, +0.1 # [hidden]
# Positioning mode
bind = Super+Alt, Space, togglefloating,
bind = Super+Alt, F, fullscreenstate, 0 3 # Toggle fake fullscreen
bind = Super, F, fullscreen, 0
bind = Super, D, fullscreen, 1

#!
##! Workspace navigation
# Switching
#/# bind = Super, Hash,, # Focus workspace # (1, 2, 3, 4, ...)
bind = Super, 1, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 1 # [hidden]
bind = Super, 2, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 2 # [hidden]
bind = Super, 3, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 3 # [hidden]
bind = Super, 4, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 4 # [hidden]
bind = Super, 5, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 5 # [hidden]
bind = Super, 6, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 6 # [hidden]
bind = Super, 7, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 7 # [hidden]
bind = Super, 8, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 8 # [hidden]
bind = Super, 9, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 9 # [hidden]
bind = Super, 0, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 10 # [hidden]

#/# bind = Super, Scroll ↑/↓,, # Workspace: focus left/right
bind = Super, mouse_up, workspace, +1 # [hidden]
bind = Super, mouse_down, workspace, -1 # [hidden]
bind = Ctrl+Super, mouse_up, workspace, r+1 # [hidden]
bind = Ctrl+Super, mouse_down, workspace, r-1 # [hidden]
#/# bind = Ctrl+Super, ←/→,, # Workspace: focus left/right
bind = Ctrl+Super, Right, workspace, r+1 # [hidden]
bind = Ctrl+Super, Left, workspace, r-1 # [hidden]
#/# bind = Ctrl+Super+Alt, ←/→,, # Workspace: focus non-empty left/right
bind = Ctrl+Super+Alt, Right, workspace, m+1 # [hidden]
bind = Ctrl+Super+Alt, Left, workspace, m-1 # [hidden]
#/# bind = Super, Page_↑/↓,, # Workspace: focus left/right
bind = Super, Page_Down, workspace, +1 # [hidden]
bind = Super, Page_Up, workspace, -1 # [hidden]
bind = Ctrl+Super, Page_Down, workspace, r+1 # [hidden]
bind = Ctrl+Super, Page_Up, workspace, r-1 # [hidden]
## Special
bind = Super, S, togglespecialworkspace,
bind = Super, mouse:275, togglespecialworkspace,

##! Workspace management
# Move window to workspace Super + Alt + [0-9]
#/# bind = Super+Alt, Hash,, # Window: move to workspace # (1, 2, 3, 4, ...)
bind = Super+Alt, 1, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 1 # [hidden]
bind = Super+Alt, 2, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 2 # [hidden]
bind = Super+Alt, 3, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 3 # [hidden]
bind = Super+Alt, 4, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 4 # [hidden]
bind = Super+Alt, 5, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 5 # [hidden]
bind = Super+Alt, 6, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 6 # [hidden]
bind = Super+Alt, 7, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 7 # [hidden]
bind = Super+Alt, 8, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 8 # [hidden]
bind = Super+Alt, 9, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 9 # [hidden]
bind = Super+Alt, 0, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 10 # [hidden]

bind = Ctrl+Super+Shift, Up, movetoworkspacesilent, special # [hidden]

bind = Ctrl+Super+Shift, Right, movetoworkspace, r+1 # [hidden]
bind = Ctrl+Super+Shift, Left, movetoworkspace, r-1 # [hidden]
bind = Ctrl+Super, BracketLeft, workspace, -1 # [hidden]
bind = Ctrl+Super, BracketRight, workspace, +1 # [hidden]
bind = Ctrl+Super, Up, workspace, r-5 # [hidden]
bind = Ctrl+Super, Down, workspace, r+5 # [hidden]
#/# bind = Super+Shift, Scroll ↑/↓,, # Window: move to workspace left/right
bind = Super+Shift, mouse_down, movetoworkspace, r-1 # [hidden]
bind = Super+Shift, mouse_up, movetoworkspace, r+1 # [hidden]
bind = Super+Alt, mouse_down, movetoworkspace, -1 # [hidden]
bind = Super+Alt, mouse_up, movetoworkspace, +1 # [hidden]
#/# bind = Super+Shift, Page_↑/↓,, # Window: move to workspace left/right
bind = Super+Alt, Page_Down, movetoworkspace, +1 # [hidden]
bind = Super+Alt, Page_Up, movetoworkspace, -1 # [hidden]
bind = Super+Shift, Page_Down, movetoworkspace, r+1  # [hidden]
bind = Super+Shift, Page_Up, movetoworkspace, r-1  # [hidden]
bind = Super+Alt, S, movetoworkspacesilent, special
bind = Super, P, pin

bind = Ctrl+Super, S, togglespecialworkspace, # [hidden]
bind = Alt, Tab, cyclenext # [hidden] sus keybind
bind = Alt, Tab, bringactivetotop, # [hidden] bring it to the top

#!
##! Widgets
bindr = Ctrl+Super, R, exec, killall ags agsv1 ydotool; agsv1 & # Restart widgets
bindr = Ctrl+Super+Alt, R, exec, hyprctl reload; killall agsv1 ydotool; agsv1 & # [hidden]
bind = Ctrl+Alt, Slash, exec, agsv1 run-js 'cycleMode();' # Cycle bar mode (normal, focus)
bindir = Super, Super_L, exec, agsv1 -t 'overview' # Toggle overview/launcher
bind = Super, Tab, exec, agsv1 -t 'overview' # [hidden]
bind = Super, Slash, exec, for ((i=0; i<$(hyprctl monitors -j | jq length); i++)); do agsv1 -t "cheatsheet""$i"; done # Show cheatsheet
bind = Super, B, exec, agsv1 -t 'sideleft' # Toggle left sidebar
bind = Super, A, exec, agsv1 -t 'sideleft' # [hidden]
bind = Super, O, exec, agsv1 -t 'sideleft' # [hidden]
bind = Super, N, exec, agsv1 -t 'sideright' # Toggle right sidebar
bind = Super, M, exec, agsv1 run-js 'openMusicControls.value = (!mpris.getPlayer() ? false : !openMusicControls.value);' # Toggle music controls
bind = Super, Comma, exec, agsv1 run-js 'openColorScheme.value = true; Utils.timeout(2000, () => openColorScheme.value = false);' # View color scheme and options
bind = Super, K, exec, for ((i=0; i<$(hyprctl monitors -j | jq length); i++)); do agsv1 -t "osk""$i"; done # Toggle on-screen keyboard
bind = Ctrl+Alt, Delete, exec, for ((i=0; i<$(hyprctl monitors -j | jq length); i++)); do agsv1 -t "session""$i"; done # Toggle power menu
bind = Ctrl+Super, G, exec, for ((i=0; i<$(hyprctl monitors -j | jq length); i++)); do agsv1 -t "crosshair""$i"; done # Toggle crosshair
bindle=, XF86MonBrightnessUp, exec, agsv1 run-js 'brightness.screen_value += 0.05; indicator.popup(1);' # [hidden]
bindle=, XF86MonBrightnessDown, exec, agsv1 run-js 'brightness.screen_value -= 0.05; indicator.popup(1);' # [hidden]
bindl  = , XF86AudioMute, exec, agsv1 run-js 'indicator.popup(1);' # [hidden]
bindl  = Super+Shift,M,   exec, agsv1 run-js 'indicator.popup(1);' # [hidden]

# Testing
# bind = SuperAlt, f12, exec, notify-send "Hyprland version: $(hyprctl version | head -2 | tail -1 | cut -f2 -d ' ')" "owo" -a 'Hyprland keybind'
# bind = Super+Alt, f12, exec, notify-send "Millis since epoch" "$(date +%s%N | cut -b1-13)" -a 'Hyprland keybind'
bind = Super+Alt, f12, exec, notify-send 'Test notification' "Here's a really long message to test truncation and wrapping\nYou can middle click or flick this notification to dismiss it!" -a 'Shell' -A "Test1=I got it!" -A "Test2=Another action" -t 5000 # [hidden]
bind = Super+Alt, Equal, exec, notify-send "Urgent notification" "Ah hell no" -u critical -a 'Hyprland keybind' # [hidden]

##! Media
bindl= Super+Shift, N, exec, playerctl next || playerctl position `bc <<< "100 * $(playerctl metadata mpris:length) / 1000000 / 100"` # Next track
bindl= ,XF86AudioNext, exec, playerctl next || playerctl position `bc <<< "100 * $(playerctl metadata mpris:length) / 1000000 / 100"` # [hidden]
bindl= ,XF86AudioPrev, exec, playerctl previous # [hidden]
bindel = Super+Shift, Comma, exec, ~/.config/ags/scripts/music/adjust-volume.sh -0.03 # Raise music volume
bindel = Super+Shift, Period, exec, ~/.config/ags/scripts/music/adjust-volume.sh 0.03 # Lower music volume
bind = Super+Shift+Alt, mouse:275, exec, playerctl previous # [hidden]
bind = Super+Shift+Alt, mouse:276, exec, playerctl next || playerctl position `bc <<< "100 * $(playerctl metadata mpris:length) / 1000000 / 100"` # [hidden]
bindl= Super+Shift, B, exec, playerctl previous # Previous track
bindl= Super+Shift, P, exec, playerctl play-pause # Play/pause media
bindl= ,XF86AudioPlay, exec, playerctl play-pause # [hidden]
bindl= ,XF86AudioPause, exec, playerctl play-pause # [hidden]

#!
##! Apps
bind = Super, T, exec, # Launch foot (terminal)
bind = Super, Z, exec, Zed # Launch Zed (editor)
bind = Super, C, exec, code # Launch VSCode (editor)
bind = Super, E, exec, nautilus --new-window # Launch Nautilus (file manager)
bind = Super+Alt, E, exec, thunar # [hidden]
bind = Super, W, exec, google-chrome-stable || firefox # [hidden] Let's not give people (more) reason to shit on my rice
bind = Ctrl+Super, W, exec, firefox # Launch Firefox (browser)
bind = Super, X, exec, gnome-text-editor --new-window # Launch GNOME Text Editor
bind = Super+Shift, W, exec, wps # Launch WPS Office
bind = Super, I, exec, XDG_CURRENT_DESKTOP="gnome" gnome-control-center # Launch GNOME Settings
bind = Ctrl+Super, V, exec, pavucontrol # Launch pavucontrol (volume mixer)
bind = Ctrl+Super+Shift, V, exec, easyeffects # Launch EasyEffects (equalizer & other audio effects)
bind = Ctrl+Shift, Escape, exec, gnome-system-monitor # Launch GNOME System monitor
bind = Ctrl+Super, Slash, exec, pkill anyrun || anyrun # Toggle fallback launcher: anyrun
bind = Super+Alt, Slash, exec, pkill fuzzel || fuzzel # Toggle fallback launcher: fuzzel

# Cursed stuff
## Make window not amogus large
bind = Ctrl+Super, Backslash, resizeactive, exact 640 480 # [hidden]


