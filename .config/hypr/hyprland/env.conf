# ######### Input method ########## 
# See https://fcitx-im.org/wiki/Using_Fcitx_5_on_Wayland
env = QT_IM_MODULE, fcitx
env = XMODIFIERS, @im=fcitx
# env = GTK_IM_MODULE, wayland   # Crashes electron apps in xwayland
# env = GTK_IM_MODULE, fcitx     # My Gtk apps no longer require this to work with fcitx5 hmm  
env = SDL_IM_MODULE, fcitx
env = GLFW_IM_MODULE, ibus
env = INPUT_METHOD, fcitx

# ############ Themes #############
env = QT_QPA_PLATFORM, wayland
env = QT_QPA_PLATFORMTHEME, qt6ct
# env = QT_STYLE_OVERRIDE,kvantum
# env = WLR_NO_HARDWARE_CURSORS, 1

# ######## Screen tearing #########
# env = WLR_DRM_NO_ATOMIC, 1

# ######## Virtual environment #########
env = ILLOGICAL_IMPULSE_VIRTUAL_ENV, ~/.local/state/ags/.venv

# ############ Others #############

