# ######## Window rules ########

# Uncomment to apply global transparency to all windows:
# windowrulev2 = opacity 0.89 override 0.89 override, class:.*

# Disable blur for XWayland windows (or context menus with shadow would look weird)
windowrulev2 = noblur, xwayland:1

# Floating
windowrulev2 = float, class:^(blueberry\.py)$
windowrulev2 = float, class:^(steam)$
windowrulev2 = float, class:^(guifetch)$   # FlafyDev/guifetch
windowrulev2 = float, class:^(pavucontrol)$
windowrulev2 = size 45%, class:^(pavucontrol)$
windowrulev2 = center, class:^(pavucontrol)$
windowrulev2 = float, class:^(org.pulseaudio.pavucontrol)$
windowrulev2 = size 45%, class:^(org.pulseaudio.pavucontrol)$
windowrulev2 = center, class:^(org.pulseaudio.pavucontrol)$
windowrulev2 = float, class:^(nm-connection-editor)$
windowrulev2 = size 45%, class:^(nm-connection-editor)$
windowrulev2 = center, class:^(nm-connection-editor)$

# Tiling
windowrulev2 = tile, class:^dev\.warp\.Warp$

# Picture-in-Picture
windowrulev2 = float, title:^([Pp]icture[-\s]?[Ii]n[-\s]?[Pp]icture)(.*)$
windowrulev2 = keepaspectratio, title:^([Pp]icture[-\s]?[Ii]n[-\s]?[Pp]icture)(.*)$
windowrulev2 = move 73% 72%, title:^([Pp]icture[-\s]?[Ii]n[-\s]?[Pp]icture)(.*)$ 
windowrulev2 = size 25%, title:^([Pp]icture[-\s]?[Ii]n[-\s]?[Pp]icture)(.*)$
windowrulev2 = float, title:^([Pp]icture[-\s]?[Ii]n[-\s]?[Pp]icture)(.*)$
windowrulev2 = pin, title:^([Pp]icture[-\s]?[Ii]n[-\s]?[Pp]icture)(.*)$

# Dialog windows – float+center these windows.
windowrulev2 = center, title:^(Open File)(.*)$
windowrulev2 = center, title:^(Select a File)(.*)$
windowrulev2 = center, title:^(Choose wallpaper)(.*)$
windowrulev2 = center, title:^(Open Folder)(.*)$
windowrulev2 = center, title:^(Save As)(.*)$
windowrulev2 = center, title:^(Library)(.*)$
windowrulev2 = center, title:^(File Upload)(.*)$
windowrulev2 = float, title:^(Open File)(.*)$
windowrulev2 = float, title:^(Select a File)(.*)$
windowrulev2 = float, title:^(Choose wallpaper)(.*)$
windowrulev2 = float, title:^(Open Folder)(.*)$
windowrulev2 = float, title:^(Save As)(.*)$
windowrulev2 = float, title:^(Library)(.*)$
windowrulev2 = float, title:^(File Upload)(.*)$


# --- Tearing ---
windowrulev2 = immediate, title:.*\.exe
windowrulev2 = immediate, class:^(steam_app)

# No shadow for tiled windows (matches windows that are not floating).
windowrulev2 = noshadow, floating:0

# ######## Workspace rules ########
workspace = special:special, gapsout:30

# ######## Layer rules ########
layerrule = xray 1, .*
# layerrule = noanim, .*
layerrule = noanim, walker
layerrule = noanim, selection
layerrule = noanim, overview
layerrule = noanim, anyrun
layerrule = noanim, indicator.*
layerrule = noanim, osk
layerrule = noanim, hyprpicker

layerrule = noanim, noanim
layerrule = blur, gtk-layer-shell
layerrule = ignorezero, gtk-layer-shell
layerrule = blur, launcher
layerrule = ignorealpha 0.5, launcher
layerrule = blur, notifications
layerrule = ignorealpha 0.69, notifications
layerrule = blur, logout_dialog # wlogout

# ags
layerrule = animation slide left, sideleft.*
layerrule = animation slide right, sideright.*
layerrule = blur, session[0-9]*
layerrule = blur, bar[0-9]*
layerrule = ignorealpha 0.6, bar[0-9]*
layerrule = blur, barcorner.*
layerrule = ignorealpha 0.6, barcorner.*
layerrule = blur, dock[0-9]*
layerrule = ignorealpha 0.6, dock[0-9]*
layerrule = blur, indicator.*
layerrule = ignorealpha 0.6, indicator.*
layerrule = blur, overview[0-9]*
layerrule = ignorealpha 0.6, overview[0-9]*
layerrule = blur, cheatsheet[0-9]*
layerrule = ignorealpha 0.6, cheatsheet[0-9]*
layerrule = blur, sideright[0-9]*
layerrule = ignorealpha 0.6, sideright[0-9]*
layerrule = blur, sideleft[0-9]*
layerrule = ignorealpha 0.6, sideleft[0-9]*
layerrule = blur, indicator.*
layerrule = ignorealpha 0.6, indicator.*
layerrule = blur, osk[0-9]*
layerrule = ignorealpha 0.6, osk[0-9]*
