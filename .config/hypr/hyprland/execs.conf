# Bar, wallpaper
exec-once = swww-daemon --format xrgb
exec-once = bash ~/.config/hypr/custom/scripts/__restore_video_wallpaper.sh
exec-once = /usr/lib/geoclue-2.0/demos/agent & gammastep
exec-once = agsv1 &

# Input method
exec-once = fcitx5

# Core components (authentication, lock screen, notification daemon)
exec-once = gnome-keyring-daemon --start --components=secrets
exec-once = /usr/lib/polkit-gnome/polkit-gnome-authentication-agent-1 || /usr/libexec/polkit-gnome-authentication-agent-1
exec-once = hypridle
exec-once = dbus-update-activation-environment --all
exec-once = sleep 1 && dbus-update-activation-environment --systemd WAYLAND_DISPLAY XDG_CURRENT_DESKTOP # Some fix idk
exec-once = hyprpm reload

# Audio
exec-once = easyeffects --gapplication-service

# Clipboard: history
# exec-once = wl-paste --watch cliphist store &
exec-once = wl-paste --type text --watch cliphist store
exec-once = wl-paste --type image --watch cliphist store

# Cursor
exec-once = hyprctl setcursor Bibata-Modern-Classic 24

# Polkit
exec-once = /usr/libexec/polkit-mate-authentication-agent-1
