// Made to be pixel-perfect with 11pt font size
// 1rem = 11pt = 14.6666666667px
$bar_ws_width: 1.774rem;
$bar_ws_width_focus: 0.614rem;
$bar_ws_width_focus_active: 2.045rem;

@mixin bar-group-rounding {
    @include small-rounding;
}

.bar-height {
    min-height: 2.727rem;
}

.bar-bg {
    background-color: $layer0;
    min-height: 2.727rem;
}

.bar-bg-focus {
    background-color: $layer0;
    min-height: 1.364rem;
}

.bar-bg-nothing {
    background-color: $layer0;
    min-height: 2px;
}

.bar-bg-focus-batterylow {
    background-color: mix($layer0, $errorContainer, 80%);
}

.bar-sidespace {
    min-width: 1.5rem;
}

.bar-group-margin {
    padding: 0.273rem 0rem;
}

.bar-group {
    background-color: $layer1;
}

.bar-group-borderless {
    background-color: transparent;
}

.bar-group-pad {
    padding: 0.205rem;
}

.bar-group-pad-less {
    padding: 0rem 0.681rem;
}

.bar-group-pad-system {
    padding: 0rem 0.341rem;
}

.bar-group-pad-music {
    padding-right: 1.023rem;
    padding-left: 0.341rem;
}

.bar-group-standalone {
    @include bar-group-rounding;
    -gtk-outline-radius: 1.364rem;
}

.bar-group-round {
    border-radius: 10rem;
    -gtk-outline-radius: 10rem;
}

.bar-group-middle {
    border-radius: 0.477rem;
    -gtk-outline-radius: 0.477rem;
}

.bar-group-left {
    border-radius: 0.477rem;
    -gtk-outline-radius: 0.477rem;
    border-top-left-radius: 1.364rem;
    border-bottom-left-radius: 1.364rem;
}

.bar-group-right {
    border-radius: 0.477rem;
    -gtk-outline-radius: 0.477rem;
    border-top-right-radius: 1.364rem;
    border-bottom-right-radius: 1.364rem;
}

.bar-sidemodule {
    min-width: 26rem;
}

.bar-ws-width {
    min-width: 18.341rem;
}


.bar-ws-container {
    transition: 700ms cubic-bezier(0.1, 1, 0, 1);
}

.bar-ws {
    font-size: 1.02rem;
    font-weight: 600;
    min-width: $bar_ws_width;
    color: $workspaceOnLayer1Inactive;
}

.bar-ws-active {
    background-color: $workspaceLayer3;
    color: $workspaceOnLayer3;
}

.bar-ws-occupied {
    background-color: $layer2;
    color: $workspaceOnLayer2;
}

// Focus is the bar mode name, not the workspace state!

.bar-ws-focus {
    background-color: $surfaceVariant;
    min-width: $bar_ws_width_focus;
}

.bar-ws-focus-active {
    min-width: $bar_ws_width_focus_active;
    background-color: $onLayer0;
}

.bar-ws-focus-occupied {
    background-color: $secondaryContainer;
}

.bar-clock-box {
    margin: 0rem 0.682rem;
}

.bar-time {
    @include titlefont;
    font-size: 1.2727rem;
    color: $timeOnLayer1;
}

.bar-date {
    color: $dateOnLayer1;
}

.bar-batt {
    @include full-rounding;
    @include element_decel;
    min-height: 1.77rem;
    min-width: 1.77rem;
    border-radius: 10rem;
    color: $battOnLayer2;
}

.bar-batt-txt {
    color: $battOnLayer1;
}

.bar-batt-circprog {
    @include fluent_decel_long;
    min-width: 0.136rem; // line width
    min-height: 1.636rem;
    padding: 0rem;
    background-color: $battLayer2;
    color: $battOnLayer2;
}

.bar-batt-circprog-borderless {
    background-color: transparent;
}

.bar-batt-circprog-low {
    background-color: $error;
    color: $errorContainer;
}


.bar-batt-low {
    background-color: $error;
    color: $errorContainer;
}

.bar-ram-icon {
    @include full-rounding;
    @include element_decel;
    min-height: 1.77rem;
    min-width: 1.77rem;
    border-radius: 10rem;
    color: $ramOnLayer2;
}

.bar-ram-circprog {
    @include fluent_decel_long;
    min-width: 0.136rem; // line width
    min-height: 1.636rem;
    padding: 0rem;
    background-color: $ramLayer2;
    color: $ramOnLayer2;
}

.bar-ram-circprog-borderless {
    background-color: transparent;
}

.bar-ram-txt {
    color: $ramOnLayer1;
}

.bar-swap-icon {
    @include full-rounding;
    @include element_decel;
    min-height: 1.77rem;
    min-width: 1.77rem;
    border-radius: 10rem;
    color: $swapOnLayer2;
}

.bar-swap-circprog {
    @include fluent_decel_long;
    min-width: 0.136rem; // line width
    min-height: 1.636rem;
    padding: 0rem;
    background-color: $swapLayer2;
    color: $swapOnLayer2;
}

.bar-swap-circprog-borderless {
    background-color: transparent;
}

.bar-swap-txt {
    color: $swapOnLayer1;
}

.bar-cpu-icon {
    @include full-rounding;
    @include element_decel;
    min-height: 1.77rem;
    min-width: 1.77rem;
    border-radius: 10rem;
    color: $cpuOnLayer2;
}

.bar-cpu-circprog {
    @include fluent_decel_long;
    min-width: 0.136rem; // line width
    min-height: 1.636rem;
    padding: 0rem;
    background-color: $cpuLayer2;
    color: $cpuOnLayer2;
}

.bar-cpu-circprog-borderless {
    background-color: transparent;
}

.bar-cpu-txt {
    color: $cpuOnLayer1;
}

.bar-music-playstate {
    @include element_decel;
    min-height: 1.77rem;
    min-width: 1.77rem;
    border-radius: 10rem;
    color: $musicOnLayer2;
}

.bar-music-circprog {
    @include fluent_decel_long;
    min-width: 0.136rem; // line width
    min-height: 1.636rem;
    padding: 0rem;
    background-color: $musicLayer2;
    color: $musicOnLayer2;
}

.bar-music-circprog-borderless {
    background-color: transparent;
}

.bar-music-playstate-playing {
    min-height: 1.77rem;
    min-width: 1.77rem;
    border-radius: 10rem;
    color: $musicOnLayer2;
}

.bar-music-playstate-txt {
    transition: 100ms cubic-bezier(0.05, 0.7, 0.1, 1);
    @include icon-material;
}

.bar-music-txt {
    color: $musicOnLayer1;
}

.bar-music-cover {
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% auto;
    min-width: 11.932rem;
}

.bar-music-extended-bg {
    border-radius: 1.364rem;
    min-width: 34.091rem;
}

.bar-music-hide-false {
    @include menu_decel;
    transition-duration: 100ms;
    opacity: 1;
}

.bar-music-hide-true {
    @include menu_accel;
    transition-duration: 100ms;
    opacity: 0;
}

.bar-corner-spacing {
    min-width: $rounding_large;
    min-height: $rounding_large;
}

.corner {
    background-color: $layer0;
    @include large-rounding;
}

.corner-black {
    background-color: $black; // Hard code: fake screen corner
    @include large-rounding;
}

.bar-wintitle-topdesc {
    margin-top: -0.136rem;
    margin-bottom: -0.341rem;
    color: $windowtitleOnLayer0Inactive;
}

.bar-wintitle-txt {
    color: $windowtitleOnLayer0;
}

.bar-space-button {
    padding: 0.341rem;
}

.bar-space-button>box:first-child {
    @include full-rounding;
    padding: 0rem 0.682rem;
}

.bar-space-button-leftmost {
    box {
        margin: 0rem 0.682rem;
    }
}

.bar-space-area-rightmost>box {
    padding-right: 2.386rem;
}

.bar-systray {
    @include full-rounding;
    margin: 0.137rem 0rem;
    padding: 0rem 0.682rem;
}

.bar-systray-item {
    @include full-rounding;
    @include element_decel;
    @include symbolic-icon;
    min-height: 1.032rem;
    min-width: 1.032rem;
    font-size: 1.032rem;
    color: $trayOnLayer0;
}

.bar-statusicons {
    @include full-rounding;
    @include element_decel;
    margin: 0.273rem;
    padding: 0rem 0.614rem;
}

.bar-statusicons-active {
    background-color: $layer0Active;
    color: $onLayer0Active;
}

.bar-util-btn {
    @include full-rounding;
    @include element_decel;
    min-height: 1.77rem;
    min-width: 1.77rem;
    background-color: $utilsLayer2;
    color: $utilsOnLayer2;
}

.bar-util-btn-borderless {
    background-color: transparent;
}

.bar-util-btn:hover,
.bar-util-btn:focus {
    background-color: $layer2Hover;
}

.bar-util-btn:active {
    background-color: $layer2Active;
}

.bar-spaceright {
    color: $barspacerightOnLayer0;
}

.bar-bluetooth-device {
    @include full-rounding;
    @include symbolic-icon;
    min-height: 1.032rem;
    min-width: 1.032rem;
    font-size: 1.032rem;
    padding: 0.205rem 0.341rem;
}
