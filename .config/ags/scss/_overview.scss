.overview-window {
    margin-top: 2.727rem;
}

.overview-search-box {
    @include element_decel;
    @include large-rounding;
    @include elevation-border;
    @include elevation2;
    min-width: 13.636rem;
    min-height: 3.409rem;
    padding: 0rem 1.364rem;
    padding-right: 2.864rem;
    background-color: $background;
    color: $onBackground;

    selection {
        background-color: $secondary;
        color: $onSecondary;
    }

    caret-color: transparent;
}

.overview-search-box-extended {
    min-width: 25.909rem;
    caret-color: $onSecondaryContainer;
}

.overview-search-prompt {
    color: $subtext;
}

.overview-search-icon {
    margin: 0rem 1.023rem;
}

.overview-search-prompt-box {
    margin-left: -18.545rem;
    margin-right: $elevation_margin + 0.068rem;
}

.overview-search-icon-box {
    margin-left: -18.545rem;
    margin-right: $elevation_margin + 0.068rem;
}

.overview-search-results {
    // min-height: 2.813rem;
    // min-height: 37.5rem;
    @include large-rounding;
    @include elevation-border;
    @include elevation2;
    min-width: 28.773rem;
    padding: 0.682rem;
    background-color: $layer0;
    color: $onLayer0;
}

.overview-search-results-icon {
    margin: 0rem 0.682rem;
    font-size: 2.386rem;
    min-width: 2.386rem;
    min-height: 2.386rem;
}

.overview-search-results-txt {
    margin-right: 0.682rem;
}

.overview-search-results-txt-cmd {
    margin-right: 0.682rem;
    @include techfont;
    font-size: 1.227rem;
}

.overview-search-result-btn {
    @include normal-rounding;
    padding: 0.341rem;
    min-width: 2.386rem;
    min-height: 2.386rem;

    caret-color: transparent;
}

.overview-search-result-btn:hover,
.overview-search-result-btn:focus {
    background-color: $layer2;
}

.overview-search-result-btn:active {
    background-color: $layer2Hover;
}

.overview-tasks {
    @include large-rounding;
    @include elevation-border;
    @include elevation2;
    padding: 0.341rem;
    background-color: $background;
    color: $onBackground;
}

.overview-tasks-workspace {
    @include normal-rounding;
    // @include elevation-border;
    margin: 0.341rem;
    background-color: $layer1;
}

.overview-tasks-workspace-number {
    @include mainfont;
    color: $onSurfaceVariant;
}

.overview-tasks-window {
    @include normal-rounding;
    @include menu_decel;
    background-color: transparentize($layer3, 0.2);
    color: $onSurface;
    border: 0.068rem solid $surfaceContainerHighest;
}

.overview-tasks-window:hover,
.overview-tasks-window:focus {
    background-color: transparentize($secondaryContainer, 0.3);
}

.overview-tasks-window:active {
    background-color: transparentize($secondaryContainer, 0);
}

.overview-tasks-window-selected {
    background-color: transparentize($secondaryContainer, 0.3);
}

.overview-tasks-window-dragging {
    opacity: 0.2;
}
