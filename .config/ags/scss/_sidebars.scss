$sidebar_chat_textboxareaColor: mix($onSurfaceVariant, $surfaceVariant, 40%);

@mixin group-padding {
    padding: 0.341rem;
}

.sidebar-right {
    @include menu_decel;
    @include elevation-border;
    @include elevation2;
    border-radius: $rounding_large - $elevation_margin + 0.068rem;
    min-width: 27.818rem;
    background-color: $background;
    padding: 1.023rem;
}

.sidebar-left {
    @include menu_decel;
    @include elevation-border;
    @include elevation2;
    border-radius: $rounding_large - $elevation_margin + 0.068rem;
    min-width: 27.818rem;
    background-color: $background;
    padding: 1.023rem;
}

.sidebar-group {
    @include normal-rounding;
    @include group-padding;
    background-color: $layer1;
}

.sidebar-group-nopad {
    @include normal-rounding;
    background-color: $layer1;
}

.sidebar-group-invisible {
    @include group-padding;
}

.sidebar-group-invisible-morehorizpad {
    padding: 0.341rem 0.682rem;
}

.sidebar-togglesbox {
    @include full-rounding;
    @include group-padding;
    background-color: $layer1;
}

.sidebar-iconbutton {
    @include full-rounding;
    @include element_decel;
    color: $onSurface;
    min-width: 2.727rem;
    min-height: 2.727rem;
}

.sidebar-iconbutton:hover,
.sidebar-iconbutton:focus {
    background-color: $layer1Hover;
}

.sidebar-iconbutton:active {
    background-color: $layer1Active;
}

.sidebar-button-active {
    background-color: $primary;
    color: $onPrimary;
}

.sidebar-button-active:hover,
.sidebar-button-active:focus {
    background-color: mix($primary, $layer1Hover, 70%);
}

.sidebar-button-active:active {
    background-color: mix($primary, $layer1Active, 40%);
}

.sidebar-buttons-separator {
    min-width: 0.068rem;
    min-height: 0.068rem;
    background-color: $onSurfaceVariant;
}

.sidebar-navrail {
    padding: 0rem $rounding_medium;
}

.sidebar-navrail-btn>box>label {
    @include full-rounding;
    @include menu_decel;
}

.sidebar-navrail-btn:hover>box>label:first-child,
.sidebar-navrail-btn:focus>box>label:first-child {
    background-color: $layer1Hover;
}

.sidebar-navrail-btn:active>box>label:first-child {
    background-color: $layer1Active;
}

.sidebar-navrail-btn-active>box>label:first-child {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.sidebar-navrail-btn-active:hover>box>label:first-child,
.sidebar-navrail-btn-active:focus>box>label:first-child {
    background-color: mix($secondaryContainer, $layer1Hover, 90%);
    color: mix($onSecondaryContainer, $layer1Hover, 90%);
}

.sidebar-sysinfo-grouppad {
    padding: 1.159rem;
}

.sidebar-memory-ram-circprog {
    @include fluent_decel_long;
    min-width: $rounding_small;
    min-height: 4.091rem;
    padding: 0.409rem;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
    font-size: 0px;
}

.sidebar-memory-swap-circprog {
    @include fluent_decel_long;
    min-width: $rounding_small;
    min-height: 2.255rem;
    padding: 0.409rem;
    margin: 0.918rem;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
    font-size: 0px;
}

.sidebar-cpu-circprog {
    min-width: $rounding_small;
    min-height: 3.409rem;
    padding: 0.409rem;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
    @include fluent_decel_long;
    font-size: 0px;
}

.sidebar-scrollbar {
    trough {
        @include full-rounding;
        min-width: 0.545rem;
        background-color: transparent;
    }

    slider {
        @include full-rounding;
        @include element_decel;
        min-width: 0.273rem;
        min-height: 2.045rem;
        background-color: transparentize($onSurfaceVariant, 0.7);
    }

    slider:hover,
    slider:focus {
        background-color: transparentize($onSurfaceVariant, 0.6);
    }

    slider:active {
        background-color: transparentize($onSurface, 0.5);
    }
}

.sidebar-calendar-btn-arrow {
    @include full-rounding;
    background-color: $layer2;
    min-width: 1.705rem;
    min-height: 1.705rem;

    &:hover,
    &:focus {
        background-color: $layer2Hover;
    }

    &:active {
        background-color: $layer2Active;
    }
}

.sidebar-calendar-btn {
    @include full-rounding;
    @include element_decel;
    min-height: 2.523rem;
    min-width: 2.523rem;
    color: $onSurface;
}

.sidebar-calendar-btn:hover,
.sidebar-calendar-btn:focus {
    background-color: $hovercolor;
}

.sidebar-calendar-btn:active {
    background-color: $activecolor;
}

.sidebar-calendar-btn-txt {
    margin-left: -10.341rem;
    margin-right: -10.341rem;
}

.sidebar-calendar-btn-today {
    background-color: $primary;
    color: $onPrimary;
}

.sidebar-calendar-btn-today:hover,
.sidebar-calendar-btn-today:focus {
    background-color: mix($primary, $hovercolor, 70%);
}

.sidebar-calendar-btn-today:active {
    background-color: mix($primary, $hovercolor, 40%);
}

.sidebar-calendar-btn-othermonth {
    color: $outline;
}

.sidebar-calendar-header {
    margin: 0.341rem;
}

.sidebar-calendar-monthyear-btn {
    @include full-rounding;
    @include element_decel;
    padding: 0rem 0.682rem;
    background-color: $layer2;
    color: $onSurface;
}

.sidebar-calendar-monthyear-btn:hover,
.sidebar-calendar-monthyear-btn:focus {
    background-color: $hovercolor;
}

.sidebar-calendar-monthyear-btn:active {
    background-color: $activecolor;
}

.sidebar-calendar-monthshift-btn {
    @include full-rounding;
    @include element_decel;
    min-width: 2.045rem;
    min-height: 2.045rem;
    background-color: $layer2;
    color: $outline;
}

.sidebar-calendar-monthshift-btn:hover {
    background-color: $hovercolor;
}

.sidebar-calendar-monthshift-btn:active {
    background-color: $activecolor;
}

.sidebar-calendar-collapsed-pill {
    @include full-rounding;
    background-color: $layer2;
    min-width: 1.705rem;
    min-height: 1.705rem;
    padding-left: 0.341rem;
    padding-right: 0.341rem;

    &:hover,
    &:focus {
        background-color: $layer2Hover;
    }

    &:active {
        background-color: $layer2Active;
    }
}

.sidebar-todo-item {
    @include small-rounding;
    margin-right: 0.545rem;
    // padding: 0.341rem;
    background-color: $layer2;
    color: $onLayer2;
}

.sidebar-todo-txt {
    margin: 0.682rem;
    margin-bottom: 0rem;
}

.sidebar-todo-actions {
    margin: 0.341rem;
    margin-top: 0rem;
    margin-right: 0rem;
}

.sidebar-todo-item-action {
    @include element_decel;
    border-radius: 9999px;
    min-width: 1.705rem;
    min-height: 1.705rem;
}

.sidebar-todo-item-action:hover,
.sidebar-todo-item-action:focus {
    background-color: $layer2Hover;
}

.sidebar-todo-item-action:active {
    background-color: $layer2Active;
}

.sidebar-todo-crosser {
    transition: margin 200ms cubic-bezier(0.1, 1, 0, 1), background-color 0ms;
    min-width: 0rem;
}

.sidebar-todo-crosser-crossed {
    background-color: $onBackground;
}

.sidebar-todo-crosser-removed {
    background-color: $error;
}

.sidebar-todo-new {
    @include full-rounding;
    @include element_decel;
    background-color: $layer2;
    color: $onLayer2;
    margin: 0.341rem;
    padding: 0.205rem 0.545rem;
}

.sidebar-todo-new,
.sidebar-todo-new:focus {
    color: $onSecondaryContainer;
    background-color: $secondaryContainer;
}

.sidebar-todo-new:active {
    color: $onPrimaryContainer;
    background-color: $primaryContainer;
}

.sidebar-todo-add {
    @include element_decel;
    @include small-rounding;
    min-width: 1.705rem;
    min-height: 1.705rem;
    color: $onSecondaryContainer;
    border: 0.068rem solid $onSurface;
}

.sidebar-todo-add:hover,
.sidebar-todo-add:focus {
    background-color: $surfaceContainerHigh;
}

.sidebar-todo-add:active {
    background-color: $surfaceContainerHighest;
}

.sidebar-todo-add-available {
    @include element_decel;
    @include small-rounding;
    min-width: 1.705rem;
    min-height: 1.705rem;
    background-color: $primary;
    color: $onPrimary;
    border: 0.068rem solid $primary;
}

.sidebar-todo-add-available:hover,
.sidebar-todo-add-available:focus {
    background-color: mix($primary, $hovercolor, 70%);
}

.sidebar-todo-add-available:active {
    background-color: mix($primary, $hovercolor, 40%);
}

.sidebar-todo-entry {
    @include element_decel;
    @include small-rounding;
    background-color: $surfaceVariant;
    color: $onSurfaceVariant;
    caret-color: $onSurfaceVariant;
    margin: 0rem 0.341rem;
    min-height: 1.773rem;
    min-width: 0rem;
    padding: 0.205rem 0.682rem;
    border: 0.068rem solid $outline;
}

.sidebar-todo-entry:focus {
    border: 0.068rem solid $onSurfaceVariant;
}

.sidebar-module {
    @include normal-rounding;
    @include group-padding;
    background-color: $layer1;
    padding: 0.682rem;
}

.sidebar-module-btn-arrow {
    @include full-rounding;
    @include icon-material;
    min-width: 1.705rem;
    min-height: 1.705rem;
    background-color: $layer2;

    &:hover,
    &:focus {
        background-color: $layer2Hover;
    }

    &:active {
        background-color: $layer2Active;
    }
}

.sidebar-module-scripts-button {
    @include full-rounding;
    @include icon-material;
    background-color: $layer1;
    min-width: 1.705rem;
    min-height: 1.705rem;

    &:hover,
    &:focus {
        background-color: $layer1Hover;
    }

    &:active {
        background-color: $layer1Active;
    }
}

$colorpicker_rounding: 0.341rem;

.sidebar-module-colorpicker-wrapper {
    padding: 0.341rem;
}

.sidebar-module-colorpicker-cursorwrapper {
    padding: 0.341rem 0.136rem;
}

.sidebar-module-colorpicker-hue {
    min-height: 13.636rem;
    min-width: 1.091rem;
    border-radius: $colorpicker_rounding;
}

.sidebar-module-colorpicker-hue-cursor {
    background-color: $onBackground;
    border: 0.136rem solid $onBackground;
    min-height: 0.136rem;
    margin-top: -0.136rem;
    border-radius: $colorpicker_rounding;
}

.sidebar-module-colorpicker-saturationandlightness-wrapper {
    padding: 0.341rem;
}

.sidebar-module-colorpicker-saturationandlightness {
    min-height: 13.636rem;
    min-width: 13.636rem;
    border-radius: $colorpicker_rounding;
}

.sidebar-module-colorpicker-saturationandlightness-cursorwrapper {
    padding: 0.341rem;
    margin-top: -0.409rem;
    margin-left: -0.409rem;
}

.sidebar-module-colorpicker-saturationandlightness-cursor {
    @include full-rounding;
    border: 0.136rem solid white;
    min-width: 0.682rem;
    min-height: 0.682rem;
    margin-top: -0.409rem;
    margin-left: -0.409rem;
}

.sidebar-module-colorpicker-result-area {
    padding: 0.341rem;
}

.sidebar-module-colorpicker-result-box {
    border-radius: $colorpicker_rounding;
    min-width: 2.045rem;
    min-height: 0.682rem;
    padding: 0.341rem;
}

.sidebar-module-csscalc-valuebox {
    @include small-rounding;
    padding: 0.477rem;
    background-color: $layer2;
    color: $onLayer2;
}

.sidebar-module-csscalc-valuebox-copybtn {
    @include verysmall-rounding;
    @include element_decel;
    min-width: 1.705rem;
    min-height: 1.705rem;
    background-color: $layer2;
    color: $onLayer2;

    &:hover,
    &:focus {
        background-color: $layer2Hover;
    }

    &:active {
        background-color: $layer2Active;
    }
}

.sidebar-icontabswitcher {
    @include full-rounding;
    @include group-padding;
    background-color: $layer1;
}

.sidebar-chat-providerswitcher {
    @include small-rounding;
    padding: 0.477rem 0.682rem;
    background-color: $surfaceContainerHigh;
    color: $onSurfaceVariant;
}

.sidebar-chat-viewport {
    @include element_decel;
    padding: 0.682rem 0rem;
}

.sidebar-chat-textarea {
    @include normal-rounding;
    background-color: $layer1;
    color: $onLayer1;
    padding: 0.682rem;
}

.sidebar-chat-entry {
    color: $onSurfaceVariant;
    caret-color: $onSurfaceVariant;
    min-height: 1.773rem;
    min-width: 0rem;
}

.sidebar-chat-wrapper {
    transition: 400ms cubic-bezier(0.1, 1, 0, 1);
}

.sidebar-chat-wrapper-extended {
    min-height: 7.500rem;
}

.sidebar-chat-send {
    @include element_decel;
    min-width: 1.705rem;
    min-height: 1.705rem;
    border-radius: $rounding_medium - 0.681rem;
}

.sidebar-chat-send:hover,
.sidebar-chat-send:focus {
    background-color: $surfaceBright;
}

.sidebar-chat-send:active {
    background-color: $surfaceVariant;
}

.sidebar-chat-send-available {
    background-color: $primary;
    color: $onPrimary;
}

.sidebar-chat-send-available:hover,
.sidebar-chat-send-available:focus {
    background-color: mix($primary, $hovercolor, 70%);
}

.sidebar-chat-send-available:active {
    background-color: mix($primary, $hovercolor, 40%);
}

.sidebar-chat-messagearea {
    margin: 0.341rem;
}

.sidebar-chat-message {
    @include normal-rounding;
    @include group-padding;
    background-color: $layer1;
}

$skeleton-accent: mix($secondary, $onSecondary, 50%);

@keyframes sidebar-chat-message-skeletonline-anim {
    0% {
        background-position: 175% 0%;
    }

    100% {
        background-position: 50% 0%;
    }
}

.sidebar-chat-message-skeletonline {
    border-radius: $rounding_verysmall;
    min-height: 1.364rem;
    background-color: $layer2;
}

.sidebar-chat-message-skeletonline-offset0 {
    background-repeat: no-repeat;
    background: linear-gradient(to right, $layer3 0%, $skeleton-accent 25%, $layer3 50%, $layer3 100%);
    background-size: 500% 500%;
    animation: sidebar-chat-message-skeletonline-anim 2s linear;
    animation-iteration-count: infinite;
}

.sidebar-chat-message-skeletonline-offset1 {
    background-repeat: no-repeat;
    background: linear-gradient(to right, $layer3 0%, $layer3 50%, $skeleton-accent 75%, $layer3 100%);
    background-size: 500% 500%;
    animation: sidebar-chat-message-skeletonline-anim 2s linear;
    animation-iteration-count: infinite;
}

.sidebar-chat-message-skeletonline-offset2 {
    margin-right: 5.795rem;
    background-repeat: no-repeat;
    background: linear-gradient(to right, $layer3 0%, $layer3 25%, $skeleton-accent 50%, $layer3 75%, $layer3 100%);
    background-size: 500% 500%;
    animation: sidebar-chat-message-skeletonline-anim 2s linear;
    animation-iteration-count: infinite;
}

.sidebar-chat-indicator {
    @include element_decel;
    @include full-rounding;
    min-width: 0.136rem;
}

.sidebar-chat-indicator-waifu {
    @include element_decel;
    @include full-rounding;
    min-width: 0.136rem;
    background-color: $onBackground;
}

.sidebar-chat-name {
    @include titlefont;
    @include small-rounding;
    padding: 0.341rem 0.818rem;
    margin: 0.341rem;
    background-color: $layer2;
    color: $onLayer2;
}

.sidebar-chat-name-user {
    background-color: $layer2;
    color: $onLayer2;
}

.sidebar-chat-name-bot {
    background-color: $secondary;
    color: $onSecondary;
}

.sidebar-chat-name-system {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.sidebar-chat-txtblock {
    margin-left: -0.136rem;
    padding: 0.341rem;
    padding-left: 0.818rem;
}

.sidebar-chat-txtblock-think {
    margin-left: -0.136rem;
    padding: 0.682rem;
    padding-left: 1.159rem;
}

.sidebar-chat-thinkblock {
    @include small-rounding;
    background-color: $layer2;
    color: $onLayer3;
}

.sidebar-chat-thinkblock-icon {
    @include verysmall-rounding;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
    min-width: 2.045rem;
    min-height: 2.045rem;
}

.sidebar-chat-thinkblock-btn-arrow {
    @include full-rounding;
    @include icon-material;
    min-width: 1.705rem;
    min-height: 1.705rem;
    background-color: $layer2;

    &:hover,
    &:focus {
        background-color: $layer2Hover;
    }

    &:active {
        background-color: $layer2Active;
    }
}

.sidebar-chat-txt {
    @include readingfont;
}

.sidebar-chat-latex {
    @include small-rounding;
    margin: 0rem 0.682rem;
    padding: 0.682rem;
    color: $onBackground;
}

.sidebar-chat-codeblock {
    @include normal-rounding;
    background-color: $layer2;
    color: $onLayer2;
    margin: 0rem 0.682rem;
}

.sidebar-chat-codeblock-topbar {
    @include mainfont;
    background-color: $layer3;
    color: $onLayer3;
    border-top-left-radius: $rounding_small;
    border-top-right-radius: $rounding_small;
    padding: 0.341rem 0.477rem;
}

.sidebar-chat-codeblock-topbar-txt {
    @include full-rounding;
    padding: 0.273rem;
}

.sidebar-chat-codeblock-topbar-btn {
    @include full-rounding;
    @include element_decel;
    padding: 0.273rem 0.477rem;
}

.sidebar-chat-codeblock-topbar-btn:hover,
.sidebar-chat-codeblock-topbar-btn:focus {
    background-color: $surfaceBright;
}

.sidebar-chat-codeblock-topbar-btn:active {
    background-color: $surfaceVariant;
}

.sidebar-chat-codeblock-code {
    @include techfont;
    padding: 0.682rem;
}

.sidebar-chat-divider {
    min-height: 1px;
    background-color: $sidebar_chat_textboxareaColor;
    margin: 0rem 0.545rem;
}

.sidebar-chat-welcome-txt {
    margin: 0rem 3.409rem;
}

.sidebar-chat-settings-toggles {
    min-width: 16.705rem;
}

.sidebar-chat-welcome-icon {
    @include full-rounding;
    font-size: 4rem;
}

.sidebar-chat-welcome-logo {
    @include full-rounding;
    @include element_decel;
    @include icon-material;
    min-height: 4.773rem;
    min-width: 4.773rem;
    font-size: 3.076rem;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.sidebar-chat-chip {
    @include element_decel;
    @include small-rounding;
    padding: 0.341rem 0.477rem;
}

.sidebar-chat-chip-action {
    @include element_decel;
    background-color: $layer2;
    color: $onSurfaceVariant;
}

.sidebar-chat-chip-action:hover,
.sidebar-chat-chip-action:focus {
    background-color: $hovercolor;
}

.sidebar-chat-chip-action:active {
    background-color: $activecolor;
}

.sidebar-chat-chip-action-active {
    color: $sidebar_chat_textboxareaColor;
    border: 0.068rem solid $sidebar_chat_textboxareaColor;
}

.sidebar-chat-chip-toggle {
    @include element_decel;
    @include small-rounding;
    padding: 0.341rem 0.477rem;
    background-color: $layer3;
    color: $onSurfaceVariant;
}

.sidebar-chat-chip-toggle:focus,
.sidebar-chat-chip-toggle:hover {
    background-color: $hovercolor;
}

.sidebar-chat-chip-toggle:active {
    background-color: $activecolor;
}

.sidebar-controlbtn {
    @include small-rounding;
    @include element_decel;
    min-height: 2.386rem;
    min-width: 2.386rem;
    color: $onSurface;
}

.sidebar-controlbtn:hover,
.sidebar-controlbtn:focus {
    background-color: $hovercolor;
}

.sidebar-controlbtn:active {
    background-color: $activecolor;
}

.sidebar-controlbtn-enabled {
    background-color: $secondaryContainer;

    label {
        color: $onSecondaryContainer;
    }
}

.sidebar-controlbtn-enabled:hover,
.sidebar-controlbtn-enabled:focus {
    background-color: mix($secondaryContainer, $onSecondaryContainer, 90%);
}

.sidebar-controlbtn-enabled:active {
    background-color: mix($secondaryContainer, $onSecondaryContainer, 75%);
}

.sidebar-expandbtn-enabled {
    min-width: 29.113rem;
}

.sidebar-waifu-heading {
    @include titlefont;
    padding: 0.341rem;
    margin-left: -0.136rem;
    padding-left: 0.818rem;
}

.sidebar-waifu-txt {
    @include mainfont;
}

.sidebar-waifu-image {
    @include normal-rounding;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

.sidebar-waifu-image-actions {
    @include element_decel;
    padding: 0.313rem;
}

$waifu_image_overlay_transparency: 0.7;

.sidebar-waifu-image-action {
    @include full-rounding;
    min-width: 1.875rem;
    min-height: 1.875rem;
    background-color: rgba(0, 0, 0, $waifu_image_overlay_transparency ); // Fixed cuz on image
    color: rgba(255, 255, 255, $waifu_image_overlay_transparency);
}

.sidebar-waifu-image-action:hover,
.sidebar-waifu-image-action:focus {
    background-color: rgba(30, 30, 30, $waifu_image_overlay_transparency);
}

.sidebar-waifu-image-action:active {
    background-color: rgba(60, 60, 60, $waifu_image_overlay_transparency);
}

.sidebar-booru-provider {
    @include titlefont;
    @include small-rounding;
    padding: 0.341rem 0.818rem;
    margin: 0.341rem;
    margin-bottom: 0rem;
    font-weight: bold;
    background-color: $primary;
    color: $onPrimary;
}

.sidebar-booru-imagegrid {
    @include normal-rounding;
}

.sidebar-booru-image {
    @include small-rounding;
    margin: 0.273rem;
    min-width: 11.932rem;
}

.sidebar-booru-image-drawingarea {
    @include small-rounding;
    min-width: 12.273rem;
    min-height: 12.273rem;
}

.sidebar-booru-image-actions {
    @include element_decel;
    margin: 0.545rem;
}

.sidebar-booru-tip-icon {
    min-width: 2.392rem;
    min-height: 2.392rem;
}

.sidebar-volmixer-stream {
    border-bottom: 0.068rem solid $outlineVariant;
    padding: 0.682rem;
    color: $onSurface;
}

.sidebar-volmixer-stream-appicon {
    font-size: 3.273rem;
}

.sidebar-volmixer-stream-slider {
    trough {
        border-radius: $rounding_verysmall;
        min-height: 1.364rem;
        min-width: 1.364rem;
        background-color: $secondaryContainer;
    }

    highlight {
        border-radius: $rounding_verysmall;
        min-height: 1.364rem;
        min-width: 1.364rem;
        background-color: $primary;
    }

    slider {
        border-radius: $rounding_verysmall;
        min-height: 1.364rem;
        min-width: 1.364rem;
    }
}

.sidebar-volmixer-status {
    color: $onSurface;
    margin: 0rem 0.682rem;
}

.sidebar-volmixer-deviceselector {
    @include small-rounding;
    padding: 0.477rem 0.682rem;
    background-color: $surfaceContainerHigh;
    color: $onSurfaceVariant;
}

.sidebar-bluetooth-device {
    padding: 0.682rem;
    @include normal-rounding;
    background-color: $layer2;
    color: $onLayer2;
}

.sidebar-bluetooth-appicon {
    @include symbolic-icon;
    font-size: 2.045rem;
}

.sidebar-bluetooth-device-remove {
    @include full-rounding;
    min-width: 2.045rem;
    min-height: 2.045rem;
    // background-color: $layer3;
    padding: 0.341rem;
}

.sidebar-bluetooth-device-remove:hover,
.sidebar-bluetooth-device-remove:focus {
    @include full-rounding;
    background-color: $layer2Hover;
    padding: 0.341rem;
}

.sidebar-wifinetworks-network {
    padding: 0.682rem;
    @include normal-rounding;
    background-color: $layer2;
    color: $onLayer2;
}

.sidebar-wifinetworks-network:hover,
.sidebar-wifinetworks-network:focus {
    background-color: $layer2Hover;
}

.sidebar-wifinetworks-network:active {
    background-color: $layer2Active;
}

.sidebar-wifinetworks-signal {
    @include symbolic-icon;
    font-size: 2.045rem;
}

.sidebar-wifinetworks-auth-box {
    @include small-rounding;
    background-color: $layer1;
    color: $onLayer1;
}

.sidebar-wifinetworks-auth-entry {
    @include small-rounding;
    color: $onLayer1;
    padding: 0.682rem;
    caret-color: $onLayer2;
}

.sidebar-wifinetworks-auth-visible {
    @include element_decel;
    @include small-rounding;
    min-width: 2.045rem;
    min-height: 2.045rem;
    margin: 0.341rem;

    &:hover,
    &:focus {
        background-color: $layer3Hover;
    }

    &:active {
        background-color: $layer3Active;
    }
}

.sidebar-wifinetworks-bandwidth {
    min-width: 6.3rem;
    padding-left: 1rem;
}

.sidebar-centermodules-bottombar-button {
    @include full-rounding;
    @include element_decel;
    min-width: 6.818rem;
    min-height: 2.25rem;
    background-color: $layer2;
    color: $onLayer2;
}

.sidebar-centermodules-bottombar-button:hover,
.sidebar-centermodules-bottombar-button:focus {
    background-color: $layer2Hover;
}

.sidebar-centermodules-bottombar-button:active {
    background-color: $layer2Active;
}

.sidebar-centermodules-scrollgradient-bottom {
    @if $transparent ==False {
        background: linear-gradient(to top, $layer1 0%, transparentize($layer1, 1) 1.023rem);
    }
}

.sidebar-centermodules-scrollgradient-bottom-contentmargin {
    margin-bottom: 1.023rem;
}

.sidebar-wifinetworks-network-button {
    @include full-rounding;
    @include element_decel;
    min-width: 6.818rem;
    min-height: 2.25rem;
    color: $onLayer3;
    background-color: $layer3;

    &:hover,
    &:focus {
        background-color: $layer3Hover;
    }

    &:active {
        background-color: $layer3Active;
    }
}