// Common colors
$hovercolor: $surfaceContainerHigh;
$activecolor: $surfaceContainerHighest;
$rounding_unsharpen: 0.136rem;
$rounding_verysmall: 0.545rem;
$rounding_small: 0.818rem;
$rounding_mediumsmall: 0.955rem;
$rounding_medium: 1.159rem;
$rounding_mediumlarge: 1.364rem;
$rounding_large: 1.705rem;

// Common rules
@mixin unsharpen-rounding {
    border-radius: $rounding_unsharpen;
}

@mixin verysmall-rounding {
    border-radius: $rounding_verysmall;
    -gtk-outline-radius: $rounding_verysmall;
}

@mixin small-rounding {
    border-radius: $rounding_small;
    -gtk-outline-radius: $rounding_small;
}

@mixin normal-rounding {
    border-radius: $rounding_medium;
    -gtk-outline-radius: $rounding_medium;
}

@mixin large-rounding {
    border-radius: $rounding_large;
    -gtk-outline-radius: $rounding_large;
}

@mixin full-rounding {
    border-radius: 9999px;
    -gtk-outline-radius: 9999px;
}

// @mixin symbolic-icon {
//    -gtk-icon-theme: 'Adwaita';
// }

@mixin titlefont {
    // Geometric sans-serif
    font-family: "Gabarito", "Poppins", "Readex Pro", "Lexend", sans-serif;
}

@mixin mainfont {
    // Other clean sans-serif
    font-family: "Rubik", "Adwaita Sans", "42dotSans", "Geist", "AR One Sans", "Reddit Sans", "Inter",
        "Roboto", "Ubuntu", "Noto Sans", sans-serif;
    // font-weight: 500;
}

@mixin icon-material {
    // Material Design Icons
    font-family: "Material Symbols Rounded", "MaterialSymbolsRounded", "Material Symbols Outlined",
        "Material Symbols Sharp";
}

@mixin icon-nerd {
    // Nerd Fonts
    font-family: "SpaceMono NF", "SpaceMono Nerd Font", "JetBrains Mono NF",
        "JetBrains Mono Nerd Font", monospace;
}

@mixin techfont {
    // Monospace for sys info n stuff. Doesn't have to be a nerd font, but it's cool.
    font-family: "JetBrains Mono NF", "JetBrains Mono Nerd Font",
        "JetBrains Mono NL", "SpaceMono NF", "SpaceMono Nerd Font", monospace;
}

@mixin readingfont {
    // The most readable fonts, for a comfortable reading experience
    // in stuff like AI chat on sidebar
    font-family: "Readex Pro", "Lexend", "Noto Sans", sans-serif;
    // font-weight: 500;
}

@mixin subtext {
    color: $subtext;
}

@mixin actiontext {
    color: $actiontext;
}

$elevation_margin: 0.476rem;

@mixin elevation2 {
    box-shadow: 0px 2px 3px transparentize($shadow, 0.55);
    margin: $elevation_margin;
}

@mixin elevation2-margin {
    margin: $elevation_margin;
}

@mixin elevation2-padding {
    padding: $elevation_margin;
}

@mixin elevation3 {
    box-shadow: 0px 2px 5px $shadow;
    margin: $elevation_margin;
}

@mixin menu_decel {
    transition: 300ms cubic-bezier(0.1, 1, 0, 1);
}

@mixin menu_decel_fast {
    transition: 170ms cubic-bezier(0.1, 1, 0, 1);
}

@mixin menu_accel {
    transition: 160ms cubic-bezier(0.38, 0.04, 1, 0.07);
}

@mixin menu_accel_fast {
    transition: 100ms cubic-bezier(0.38, 0.04, 1, 0.07);
}

@mixin fluent_decel {
    transition: 200ms cubic-bezier(0.1, 1, 0, 1);
}

@mixin fluent_decel_long {
    transition: 1000ms cubic-bezier(0.1, 1, 0, 1);
}

@mixin fluent_accel {
    transition: 150ms cubic-bezier(0.42, 0, 1, 1);
}

@mixin noanim {
    transition: 0ms;
}

@mixin anim-enter {
    transition: 200ms cubic-bezier(0.05, 0.7, 0.1, 1);
}

@mixin anim-exit {
    transition: 150ms cubic-bezier(0.3, 0, 0.8, 0.15);
}

@mixin element_decel {
    transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
}

@mixin element_bounceOut {
    transition: transform 200ms cubic-bezier(0.34, 1.56, 0.64, 1);
}

@mixin element_accel {
    transition: 300ms cubic-bezier(0.55, 0, 1, 0.45);
}

@mixin element_easeInOut {
    transition: 300ms cubic-bezier(0.85, 0, 0.15, 1);
}

@mixin elevation-border-softer {
    border-top: 1px solid mix($surface, $onSurface, 86%);
    border-left: 1px solid mix($surface, $onSurface, 86%);
    border-right: 1px solid mix($surface, $onSurface, 90%);
    border-bottom: 1px solid mix($surface, $onSurface, 90%);
}

@mixin elevation-border {
    border-top: 1px solid $surfaceContainer;
    border-left: 1px solid $surfaceContainer;
    border-right: 1px solid $surfaceContainer;
    border-bottom: 1px solid $surfaceContainer;
}

@mixin button-minsize {
    min-width: 2.727rem;
    min-height: 2.727rem;
}

$white: white;
$black: black;
