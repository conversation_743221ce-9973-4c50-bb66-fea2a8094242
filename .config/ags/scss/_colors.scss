$rootTransparency: 0.22; // Transparency = 1 - opacity
$transparency: 0.75;

// Transparent versions
@if $transparent == True {
  $background: transparentize($background, $rootTransparency);
  $surface: transparentize($surface, $transparency);
  $surfaceDim: transparentize($surfaceDim, $transparency);
  $surfaceBright: transparentize($surfaceBright, $transparency);
  $surfaceContainerLowest: transparentize($surfaceContainerLowest, $transparency);
  $surfaceContainerLow: transparentize($surfaceContainerLow, $transparency);
  $surfaceContainer: transparentize($surfaceContainer, $transparency);
  $surfaceContainerHigh: transparentize($surfaceContainerHigh, $transparency);
  $surfaceContainerHighest: transparentize($surfaceContainerHighest, $transparency);
  $surfaceVariant: transparentize($surfaceVariant, $transparency);
  $inverseSurface: transparentize($inverseSurface, $transparency);
  $surfaceTint: transparentize($surfaceTint, $transparency);
}

// Others
$subtext: $outline;
$actiontext: mix($onBackground, $background, 85%);
$black: black;
$white: white;

/// Color mappings for more chaotic, dynamic colors like the average rice ///
// General
$layer0: $background;
$onLayer0: $onBackground;
$layer0Hover: mix($layer0, $onLayer0, 85%);
$layer0Active: $surfaceContainerHigh;
$onLayer0Active: $onSurface;
$onLayer0Inactive: mix($onLayer0, $layer0, 70%);
$layer1: $surfaceContainerLow;
$onLayer1: $onSurface;
$onLayer1Inactive: mix($onLayer1, $layer1, 45%);
$onLayer1: $onSurfaceVariant;
$layer2: mix($surfaceContainer, $surfaceContainerHigh, 55%);
$onLayer2: $onSurface;
$layer3: mix($surfaceContainerHigh, $onSurface, 96%);
$onLayer3: $onSurface;
$layer1Hover: mix($layer1, $onLayer1, 85%);
$layer1Active: mix($layer1, $onLayer1, 70%);
$layer2Hover: mix($layer2, $onLayer2, 90%);
$layer2Active: mix($layer2, $onLayer2, 80%);
$layer3Hover: mix($layer3, $onLayer3, 90%);
$layer3Active: mix($layer3, $onLayer3, 80%);
// Elements
$windowtitleOnLayer0Inactive: $onLayer0Inactive;
$windowtitleOnLayer0: $onLayer0;
$barspacerightOnLayer0: $onLayer0;
$timeOnLayer1: $onLayer1;
$dateOnLayer1: $onLayer1;
$ramOnLayer1: $onLayer1;
$ramLayer2: $secondaryContainer;
$ramOnLayer2: $onSecondaryContainer;
$swapOnLayer1: $onLayer1;
$swapLayer2: $secondaryContainer;
$swapOnLayer2: $onSecondaryContainer;
$cpuOnLayer1: $onLayer1;
$cpuLayer2: $secondaryContainer;
$cpuOnLayer2: $onSecondaryContainer;
$musicOnLayer1: $onLayer1;
$musicLayer2: $secondaryContainer;
$musicOnLayer2: $onSecondaryContainer;
$utilsLayer2: $layer2;
$utilsOnLayer2: $onLayer2;
$battOnLayer1: $onLayer1;
$battLayer2: $secondaryContainer;
$battOnLayer2: $onSecondaryContainer;
$workspaceOnLayer1Inactive: $onLayer1Inactive;
$workspaceLayer3: $primary;
$workspaceOnLayer3: $onPrimary;
$workspaceOnLayer2: $onSecondaryContainer;
$trayOnLayer0: $onLayer0;
$cheatsheetTitle: $onSecondaryContainer;
$cheatsheetColors: (
    $onSecondaryContainer,
    $onSecondaryContainer,
    $onSecondaryContainer,
    $onSecondaryContainer,
    $onSecondaryContainer,
    $onSecondaryContainer,
    $onSecondaryContainer,
    $onSecondaryContainer
);

$sessionColors: (
    $onLayer1,
    $onLayer1,
    $onLayer1,
    $onLayer1,
    $onLayer1,
    $onLayer1,
    $onLayer1
);
$brightnessOnLayer0: $onLayer0;
$volumeOnLayer0: $onLayer0;

