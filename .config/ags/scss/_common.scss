* {
    selection {
        background-color: $secondary;
        color: $onSecondary;
    }

    caret-color: $onLayer2;
}

@keyframes appear {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

menu {
    @include small-rounding;
    border: 1px solid mix($surfaceContainer, $onBackground, 80%); // I hate this
    padding: 0.681rem;
    background: opacify($surfaceContainer, 1);
    color: $onSurface;
    -gtk-outline-radius: 1.159rem;

    animation-name: appear;
    animation-duration: 40ms;
    animation-timing-function: ease-out;
    animation-iteration-count: 1;
}

menubar>menuitem {
    border-radius: 0.545rem;
    -gtk-outline-radius: 0.545rem;
    min-width: 13.636rem;
    min-height: 2.727rem;
}

menu>menuitem {
    padding: 0.4em 1.5rem;
    background: transparent;
    transition: 0.2s ease background-color;
    border-radius: 0.545rem;
    -gtk-outline-radius: 0.545rem;
}

menu>menuitem:hover,
menu>menuitem:focus {
    background-color: $layer2Hover;
}

menu>menuitem:active {
    background-color: $layer2Active;
}

radio {
    @include full-rounding;
    margin: 0.273rem;
    min-width: 15px;
    min-height: 15px;
    border: 0.068rem solid $outline;
}

// radio:first-child {
//     background-color: red;
// }

radio:checked {
    min-width: 8px;
    min-height: 8px;
    background-color: $onPrimary;
    border: 0.477rem solid $primary;
}

tooltip {
    animation-name: appear;
    animation-duration: 100ms;
    animation-timing-function: ease-out;
    animation-iteration-count: 1;
    @include normal-rounding;
    background-color: opacify($color: $inverseSurface, $amount: 1);
    color: $inverseOnSurface;
}

/////////////////////////////////////////
// Emoji Chooser structure
// popover
// ├── box.emoji-searchbar
// │   ╰── entry.search
// ╰── box.emoji-toolbar
//     ├── button.image-button.emoji-section
//     ├── ...
//     ╰── button.image-button.emoji-section

popover {
    @include elevation-border-softer;
    padding: 0.681rem;
    background: $surfaceContainerHigh;
    color: $onSurface;
    border-radius: 1.159rem;
    -gtk-outline-radius: 1.159rem;

    animation-name: appear;
    animation-duration: 40ms;
    animation-timing-function: ease-out;
    animation-iteration-count: 1;
}


/////////////////////////////////////////

.configtoggle-box {
    padding: 0.205rem 0.341rem;
}

.configtoggle-reset {
    @include small-rounding;
    color: $onLayer2;
    background-color: $layer2;
    min-width: 2.045rem;
    min-height: 2.045rem;
}

.configtoggle-reset:focus,
.configtoggle-reset:hover {
    background-color: $layer2Hover;
}

.configtoggle-reset:active {
    background-color: $layer2Active;
}


.switch-bg {
    @include element_decel;
    @include full-rounding;
    border: 0.136rem solid $onSurface;
    min-width: 2.864rem;
    min-height: 1.637rem;
}

.switch-bg-true {
    background-color: $primary;
    border: 0.136rem solid $primary;
}

.switch-fg {
    @include full-rounding;
    @include menu_decel;
    background-color: $onSurface;
    color: $layer1;
    min-width: 0.819rem;
    min-height: 0.819rem;
    margin-left: 0.477rem;
}

.switch-fg-true {
    background-color: $onPrimary;
    color: $primary;
    min-width: 1.431rem;
    min-height: 1.431rem;
    margin-left: 1.431rem;
}

.switch-fg-toggling-false {
    @include menu_decel;
    min-width: 1.636rem;
    min-height: 0.819rem;
}

.segment-container {
    @include full-rounding;
    border: 0.068rem solid $outline;
}

.segment-container>*:first-child {
    border-top-left-radius: 9999px;
    border-bottom-left-radius: 9999px;
}

.segment-container>* {
    border-right: 0.068rem solid $outline;
    padding: 0.341rem 0.682rem;
}

.segment-container>*:last-child {
    border-right: 0rem solid transparent;
    border-top-right-radius: 9999px;
    border-bottom-right-radius: 9999px;
}

.segment-btn {
    color: $onSurface;
}

.segment-btn:focus,
.segment-btn:hover {
    background-color: $layer0Hover;
}

.segment-btn-enabled {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.segment-btn-enabled:hover,
.segment-btn-enabled:focus {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.multipleselection-container {}

.multipleselection-btn {
    @include small-rounding;
    padding: 0rem 0.341rem;
    border: 0.034rem solid $outline;
    color: $onSurface;
}

.multipleselection-btn:focus,
.multipleselection-btn:hover {
    background-color: $layer0Hover;
    color: $onSurface;
}

.multipleselection-btn-enabled {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.multipleselection-btn-enabled:hover,
.multipleselection-btn-enabled:focus {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.gap-v-5 {
    min-height: 0.341rem;
}

.gap-h-5 {
    min-width: 0.341rem;
}

.gap-v-10 {
    min-height: 0.682rem;
}

.gap-h-10 {
    min-width: 0.682rem;
}

.gap-v-15 {
    min-height: 1.023rem;
}

.gap-h-15 {
    min-width: 1.023rem;
}

.tab-btn {
    @include small-rounding;
    @include element_decel;
    min-height: 2.5rem;
    color: $onLayer0;
}

.tab-btn:hover {
    background-color: $layer0Hover;
}

.tab-btn:focus {
    background-color: $surfaceContainerLow;
}

.tab-btn-active>box>label {
    color: $primary;
}

.tab-indicator {
    transition: 180ms ease-in-out; // Doesn't look that good, but it syncs with the GtkStack
    min-height: 0.205rem;
    padding: 0rem 1.023rem;
    color: $primary;
}

.tab-icon {
    @include element_decel;
    @include full-rounding;
    min-width: 2.25rem;
    min-height: 2.25rem;
    font-size: 1.406rem;
    color: $onSurface;
}

.tab-icon-active {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.tab-icon-expandable {
    transition: 0ms;
    @include full-rounding;
    min-width: 2.25rem;
    min-height: 2.25rem;
    font-size: 1.406rem;
    color: $onSurface;
    padding: 0rem;
}

.tab-icon-expandable-active {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
    padding: 0rem 0.545rem;
    min-width: 9.545rem;
}

widget {
    @include small-rounding;
}

.spinbutton {
    @include small-rounding;
    color: $onLayer2;
    background-color: $layer2;
    min-width: 2.045rem;
    min-height: 2.045rem;
    caret-color: $onLayer2;

    entry {
        color: $onLayer2;
        margin: 0.477rem 0.614rem;
    }

    button {
        @include unsharpen-rounding;
        min-width: 2.045rem;
        min-height: 2.045rem;
        -gtk-outline-radius: $rounding_small;
    }

    button.up {
        // Only apply rounding to (+) button
        border-top-right-radius: $rounding_small;
        border-bottom-right-radius: $rounding_small;
    }

    // button:focus, // Looks weird after clicking cuz it'll highlight both + and -
    button:hover {
        background-color: $layer2Hover;
    }

    button:active {
        background-color: $layer2Active;
    }
}

.spinbutton-reset {
    @include small-rounding;
    color: $onLayer2;
    background-color: $layer2;
    min-width: 2.045rem;
    min-height: 2.045rem;
}

.spinbutton-reset:focus,
.spinbutton-reset:hover {
    background-color: $layer2Hover;
}

.spinbutton-reset:active {
    background-color: $layer2Active;
}

textview {
    caret-color: $onBackground;
}