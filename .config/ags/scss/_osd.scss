// .osd-window {
    // margin-top: 2.727rem;
// }

.osd-bg {
    min-width: 8.864rem;
}

.osd-value {
    @include elevation-border;
    @include elevation2;
    @include full-rounding;
    background-color: $layer0;
    padding: 0.341rem 1.023rem 0.341rem 0.682rem;
    min-width: 9.545rem;
}

.osd-progress {
    min-height: 0.682rem;
    min-width: 8.182rem;
    padding: 0rem;
    border-radius: 10rem;
    @include fluent_decel;

    trough {
        min-height: 0.682rem;
        min-width: 8.182rem;
        border-radius: 10rem;
        background-color: $layer2;
        // border: 0.068rem solid $onSecondaryContainer;
    }

    progress {
        @include fluent_decel;
        min-height: 0.409rem;
        min-width: 0.409rem;
        margin: 0rem 0.137rem;
        border-radius: 10rem;
        background-color: $onLayer2;
    }
}

.osd-label {
    font-size: 1.023rem;
    font-weight: 500;
}

.osd-value-txt {
    font-size: 1.023rem;
    font-weight: 500;
    color: $onLayer0;
}

.osd-brightness {
    color: $brightnessOnLayer0;
}
.osd-brightness-progress {
    progress {
        background-color: $brightnessOnLayer0;
    }
}
.osd-volume {
    color: $volumeOnLayer0;
}
.osd-volume-progress {
    progress {
        background-color: $volumeOnLayer0;
    }
}

.osd-notifs {
    padding-top: 0.313rem;
}

.osd-colorscheme {
    border-radius: 1.023rem;
    background-color: $layer0;
    padding: 0.313rem 0.626rem;
    @include elevation2;
}

.osd-colorscheme-settings {
    background-color: $layer1;
    padding: 0.313rem 0.626rem;
    @include small-rounding;
}

.osd-color {
    border-radius: 0.650rem;
    -gtk-outline-radius: 0.650rem;
    min-width: 2.727rem;
    min-height: 1.705rem;
    padding: 0rem 0.341rem;
    font-weight: bold;

    box {
        @include small-rounding;
        margin: 0.409rem;
    }
}

.osd-color-primary {
    background-color: $primary;
    color: $onPrimary;
    box { background-color: $onPrimary; }
}
.osd-color-primaryContainer {
    background-color: $primaryContainer;
    color: $onPrimaryContainer;
    box { background-color: $onPrimaryContainer; }
}
.osd-color-secondary {
    background-color: $secondary;
    color: $onSecondary;
    box { background-color: $onSecondary; }
}
.osd-color-secondaryContainer {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
    box { background-color: $onSecondaryContainer; }
}

.osd-color-tertiary {
    background-color: $tertiary;
    color: $onTertiary;
    box { background-color: $onTertiary; }
}
.osd-color-tertiaryContainer {
    background-color: $tertiaryContainer;
    color: $onTertiaryContainer;
    box { background-color: $onTertiaryContainer; }
}

.osd-color-error {
    background-color: $error;
    color: $onError;
    box { background-color: $onError; }
}
.osd-color-errorContainer {
    background-color: $errorContainer;
    color: $onErrorContainer;
    box { background-color: $onErrorContainer; }
}

.osd-color-surface {
    background-color: $surface;
    color: $onSurface;
    border: 0.068rem solid $outlineVariant;
    box { background-color: $onSurface; }
}

.osd-color-surfaceContainer {
    background-color: $surfaceContainer;
    color: $onSurface;
    box { background-color: $onSurface; }
}

.osd-color-inverseSurface {
    background-color: $inverseSurface;
    color: $inverseOnSurface;
    box { background-color: $onSurfaceVariant; }
}

.osd-color-surfaceVariant {
    background-color: $surfaceVariant;
    color: $onSurfaceVariant;
    box { background-color: $onSurfaceVariant; }
}
.osd-color-L1 {
    background-color: $layer1;
    color: $onLayer1;
    box { background-color: $onLayer1; }
}

.osd-color-layer0 {
    background-color: $layer0;
    color: $onLayer0;
    box { background-color: $onLayer0; }
}

.osd-settings-btn-arrow {
    @include full-rounding;
    @include icon-material;
    min-width: 1.705rem;
    min-height: 1.705rem;
    color: $onSurface;

    &:hover {
        background-color: $surfaceContainerHigh;
    }
    &:active {
        background-color: $surfaceContainerHighest;
    }
}

.osd-show {
    transition: 200ms cubic-bezier(0.1, 1, 0, 1);
}

.osd-hide {
    transition: 190ms cubic-bezier(0.85, 0, 0.15, 1);
}
