// Reset
// * {
//     all: unset;
// }
*:not(popover) { all: unset; }

// Colors
@import 'material'; // Material colors
@import './colors'; // Global color definitions. Uses material colors as base.
@import './lib_mixins';
@import 'lib_mixins_overrides';
@import './lib_classes';
@import './common'; // Context menu n stuff

// Components
@import './bar';
@import './cheatsheet';
@import './desktopbackground';
@import './dock';
@import './osd';
@import './overview';
@import './osk';
@import './sidebars';
@import './session';
@import './notifications';

// Music is put last as it might mess stuff up with pywal
@import './music'; // Everything related to music is here

// Classes for interaction
.growingRadial {
    transition: 300ms cubic-bezier(0.2, 0.0, 0, 1.0);
}
.fadingRadial {
    transition: 50ms cubic-bezier(0.2, 0.0, 0, 1.0);
}
.sidebar-pinned {
    margin: 0rem;
    border-radius: 0rem;
    border-bottom-right-radius: $rounding_large;
    border: 0rem solid;
}
