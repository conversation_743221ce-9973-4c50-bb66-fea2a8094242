// For keyboard layout in statusicons.js
// This list is not exhaustive. It just includes known/possible languages of users of my dotfiles
// Add your language here if you use multi-lang xkb input. El<PERSON>, ignore
// Note that something like "French (Canada)" should go before "French"
//                      and "English (US)" should go before "English"
export const languages = [
    {
        layout: 'us',
        name: 'English (US)',
        flag: '🇺🇸'
    },
    {
        layout: 'ru',
        name: 'Russian',
        flag: '🇷🇺',
    },
    {
        layout: 'pl',
        name: 'Polish',
        flag: '🇷🇵🇵🇱',
    },
    {
        layout: 'ro',
        name: 'Romanian',
        flag: '🇷🇴',
    },
    {
        layout: 'ca',
        name: 'French (Canada)',
        flag: '🇫🇷',
    },
    {
        layout: 'fr',
        name: 'French',
        flag: '🇫🇷',
    },
    {
        layout: 'tr',
        name: 'Turkish',
        flag: '🇹🇷',
    },
    {
        layout: 'jp',
        name: 'Japanese',
        flag: '🇯🇵',
    },
    {
        layout: 'cn',
        name: 'Chinese',
        flag: '🇨🇳',
    },
    {
        layout: 'vn',
        name: 'Vietnamese',
        flag: '🇻🇳',
    },
    {
        layout: 'undef',
        name: 'Undefined',
        flag: '🧐',
    },
]