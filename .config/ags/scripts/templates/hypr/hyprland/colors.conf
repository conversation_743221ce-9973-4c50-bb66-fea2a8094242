# exec = export SLURP_ARGS='-d -c {{ $onSecondaryContainer }}BB -b {{ $secondaryContainer }}44 -s 00000000'

general {
    col.active_border = rgba({{ $onSurface }}39)
    col.inactive_border = rgba({{ $outline }}30)
}

misc {
    background_color = rgba({{ $surface }}FF)
}

plugin {
    hyprbars {
        # Honestly idk if it works like css, but well, why not
        bar_text_font = <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, AR One Sans, Reddit Sans, Inter, Roboto, Ubuntu, Noto Sans, sans-serif
        bar_height = 30
        bar_padding = 10
        bar_button_padding = 5
        bar_precedence_over_border = true
        bar_part_of_window = true

        bar_color = rgba({{ $background }}FF)
        col.text = rgba({{ $onBackground }}FF)


        # example buttons (R -> L)
        # hyprbars-button = color, size, on-click
        hyprbars-button = rgb({{ $onBackground }}), 13, 󰖭, hyprctl dispatch killactive
        hyprbars-button = rgb({{ $onBackground }}), 13, 󰖯, hyprctl dispatch fullscreen 1
        hyprbars-button = rgb({{ $onBackground }}), 13, 󰖰, hyprctl dispatch movetoworkspacesilent special
    }
}

windowrulev2 = bordercolor rgba({{ $primary }}AA) rgba({{ $primary }}77),pinned:1