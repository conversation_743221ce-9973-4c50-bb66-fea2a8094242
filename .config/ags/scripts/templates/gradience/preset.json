{"name": "Material3_Generated", "variables": {"theme_fg_color": "#AEE5FA", "theme_text_color": "#AEE5FA", "theme_bg_color": "#1a1b26", "theme_base_color": "#1a1b26", "theme_selected_bg_color": "#AEE5FA", "theme_selected_fg_color": "rgba(0, 0, 0, 0.87)", "insensitive_bg_color": "#1a1b26", "insensitive_fg_color": "rgba(192, 202, 245, 0.5)", "insensitive_base_color": "#24283b", "theme_unfocused_fg_color": "#AEE5FA", "theme_unfocused_text_color": "#c0caf5", "theme_unfocused_bg_color": "#1a1b26", "theme_unfocused_base_color": "#1a1b26", "theme_unfocused_selected_bg_color": "#a9b1d6", "theme_unfocused_selected_fg_color": "rgba(0, 0, 0, 0.87)", "unfocused_insensitive_color": "rgba(192, 202, 245, 0.5)", "borders": "rgba(192, 202, 245, 0.12)", "unfocused_borders": "rgba(192, 202, 245, 0.12)", "warning_color": "#FDD633", "error_color": "#BA1B1B", "success_color": "#81C995", "wm_title": "#AEE5FA", "wm_unfocused_title": "rgba(192, 202, 245, 0.7)", "wm_highlight": "rgba(192, 202, 245, 0.1)", "wm_bg": "#1a1b26", "wm_unfocused_bg": "#1a1b26", "wm_button_close_icon": "#1a1b26", "wm_button_close_hover_bg": "#a9b1d6", "wm_button_close_active_bg": "#c7c7c7", "content_view_bg": "#1a1b26", "placeholder_text_color": "silver", "text_view_bg": "#1d1d1d", "budgie_tasklist_indicator_color": "#90D1F6", "budgie_tasklist_indicator_color_active": "#90D1F6", "budgie_tasklist_indicator_color_active_window": "#999999", "budgie_tasklist_indicator_color_attention": "#FDD633", "STRAWBERRY_100": "#FF9262", "STRAWBERRY_300": "#FF793E", "STRAWBERRY_500": "#F15D22", "STRAWBERRY_700": "#CF3B00", "STRAWBERRY_900": "#AC1800", "ORANGE_100": "#FFDB91", "ORANGE_300": "#FFCA40", "ORANGE_500": "#FAA41A", "ORANGE_700": "#DE8800", "ORANGE_900": "#C26C00", "BANANA_100": "#FFFFA8", "BANANA_300": "#FFFA7D", "BANANA_500": "#FFCE51", "BANANA_700": "#D1A023", "BANANA_900": "#A27100", "LIME_100": "#A2F3BE", "LIME_300": "#8ADBA6", "LIME_500": "#73C48F", "LIME_700": "#479863", "LIME_900": "#1C6D38", "BLUEBERRY_100": "#94A6FF", "BLUEBERRY_300": "#6A7CE0", "BLUEBERRY_500": "#3F51B5", "BLUEBERRY_700": "#213397", "BLUEBERRY_900": "#031579", "GRAPE_100": "#D25DE6", "GRAPE_300": "#B84ACB", "GRAPE_500": "#9C27B0", "GRAPE_700": "#830E97", "GRAPE_900": "#6A007E", "COCOA_100": "#9F9792", "COCOA_300": "#7B736E", "COCOA_500": "#574F4A", "COCOA_700": "#463E39", "COCOA_900": "#342C27", "SILVER_100": "#EEE", "SILVER_300": "#CCC", "SILVER_500": "#AAA", "SILVER_700": "#888", "SILVER_900": "#666", "SLATE_100": "#888", "SLATE_300": "#666", "SLATE_500": "#444", "SLATE_700": "#222", "SLATE_900": "#111", "BLACK_100": "#474341", "BLACK_300": "#403C3A", "BLACK_500": "#393634", "BLACK_700": "#33302F", "BLACK_900": "#2B2928", "accent_bg_color": "{{ $primary }}", "accent_fg_color": "{{ $onPrimary }}", "accent_color": "{{ $primary }}", "destructive_bg_color": "{{ $error }}", "destructive_fg_color": "{{ $onError }}", "destructive_color": "{{ $error }}", "success_bg_color": "#81C995", "success_fg_color": "rgba(0, 0, 0, 0.87)", "warning_bg_color": "#FDD633", "warning_fg_color": "rgba(0, 0, 0, 0.87)", "error_bg_color": "{{ $error }}", "error_fg_color": "{{ $onError }}", "window_bg_color": "{{ $background }}", "window_fg_color": "{{ $onBackground }}", "view_bg_color": "{{ $surface }}", "view_fg_color": "{{ $onSurface }}", "headerbar_bg_color": "mix(@dialog_bg_color, @window_bg_color, 0.5)", "headerbar_fg_color": "{{ $onSecondaryContainer }}", "headerbar_border_color": "{{ $secondaryContainer }}", "headerbar_backdrop_color": "@headerbar_bg_color", "headerbar_shade_color": "rgba(0, 0, 0, 0.09)", "card_bg_color": "{{ $background }}", "card_fg_color": "{{ $onSecondaryContainer }}", "card_shade_color": "rgba(0, 0, 0, 0.09)", "dialog_bg_color": "{{ $secondaryContainer }}", "dialog_fg_color": "{{ $onSecondaryContainer }}", "popover_bg_color": "{{ $secondaryContainer }}", "popover_fg_color": "{{ $onSecondaryContainer }}", "thumbnail_bg_color": "#1a1b26", "thumbnail_fg_color": "#AEE5FA", "shade_color": "rgba(0, 0, 0, 0.36)", "scrollbar_outline_color": "rgba(0, 0, 0, 0.5)", "sidebar_bg_color": "@window_bg_color", "sidebar_fg_color": "@window_fg_color", "sidebar_border_color": "@sidebar_bg_color", "sidebar_backdrop_color": "@sidebar_bg_color"}, "palette": {"blue_": {}, "green_": {}, "yellow_": {}, "orange_": {}, "red_": {}, "purple_": {}, "brown_": {}, "light_": {}, "dark_": {}}, "custom_css": {"gtk4": "", "gtk3": ""}, "plugins": {}}