{"No media": "No media", "Powered by Google": "Powered by Google", "Not affiliated, endorsed, or sponsored by Google.\n\nPrivacy: Chat messages aren't linked to your account,\nbut will be read by human reviewers to improve the model.": "Not affiliated, endorsed, or sponsored by Google.\n\nPrivacy: Chat messages aren't linked to your account,\nbut will be read by human reviewers to improve the model.", "Precise": "Precise", "Balanced": "Balanced", "Creative": "Creative", "Gemini's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1": "Gemini's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1", "Enhancements": "Enhancements", "Tells Gemini:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points": "Tells Gemini:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points", "Safety": "Safety", "When turned off, tells the API (not the model) \nto not block harmful/explicit content": "When turned off, tells the API (not the model) \nto not block harmful/explicit content", "History": "History", "Saves chat history\nMessages in previous chats won't show automatically, but they are there": "Saves chat history\nMessages in previous chats won't show automatically, but they are there", "Key stored in:": "Key stored in:", "To update this key, type": "To update this key, type", "Updated API Key at": "Updated API Key at", "Currently using": "Currently using", "Select ChatGPT-compatible API provider": "Select ChatGPT-compatible API provider", "Official OpenAI API.\nPricing: Free for the first $5 or 3 months, whichever is less.": "Official OpenAI API.\nPricing: Free for the first $5 or 3 months, whichever is less.", "Official Ollama API.\nPricing: Free.": "Official Ollama API.\nPricing: Free.", "A unified interface for LLMs": "A unified interface for LLMs", "An API from Tornado Softwares\nPricing: Free: 100/day\nRequires you to join their Discord for a key": "An API from Tornado Softwares\nPricing: Free: 100/day\nRequires you to join their Discord for a key", "An API from @zukixa on GitHub.\nNote: Keys are IP-locked so it's buggy sometimes\nPricing: Free: 10/min, 800/day.\nRequires you to join their Discord for a key": "An API from @zukixa on GitHub.\nNote: Keys are IP-locked so it's buggy sometimes\nPricing: Free: 10/min, 800/day.\nRequires you to join their Discord for a key", "Provider shown above": "Provider shown above", "Chat with models compatible with OpenAI's Chat Completions API.\nNot affiliated, endorsed, or sponsored by any of the providers.": "Chat with models compatible with OpenAI's Chat Completions API.\nNot affiliated, endorsed, or sponsored by any of the providers.", "The model's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1": "The model's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1", "An API key is required\nYou can grab one <u>here</u>, then enter it below": "An API key is required\nYou can grab one <u>here</u>, then enter it below", "Tells the model:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points": "Tells the model:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points", "Powered by waifu.im + other APIs": "Powered by waifu.im + other APIs", "Type tags for a random pic.\nNSFW content will not be returned unless\nyou explicitly request such a tag.\n\nDisclaimer: Not affiliated with the providers\nnor responsible for any of their content.": "Type tags for a random pic.\nNSFW content will not be returned unless\nyou explicitly request such a tag.\n\nDisclaimer: Not affiliated with the providers\nnor responsible for any of their content.", "Tags →": "Tags →", "Invalid command.": "Invalid command.", "Anime booru": "<PERSON><PERSON> booru", "Powered by yande.re and konachan": "Powered by yande.re and konachan", "An image booru. May contain NSFW content.\nWatch your back.\n\nDisclaimer: Not affiliated with the provider\nnor responsible for any of its content.": "An image booru. May contain NSFW content.\nWatch your back.\n\nDisclaimer: Not affiliated with the provider\nnor responsible for any of its content.", "Lewds": "Lewds", "Shows naughty stuff when enabled": "Shows naughty stuff when enabled", "Saves images in folders by their tags": "Saves images in folders by their tags", "Message Gemini...": "Message Gemini...", "Enter Google AI API Key...": "Enter Google AI API Key...", "Message the model...": "Message the model...", "Enter API Key...": "Enter API Key...", "Enter tags": "Enter tags", "Enter tags and/or page number": "Enter tags and/or page number", "No tag in mind? Type a page number": "No tag in mind? Type a page number", "Quick scripts": "Quick scripts", "Change screen resolution": "Change screen resolution", "Update packages": "Update packages", "Trim system generations to 5": "Trim system generations to 5", "Trim home manager generations to 5": "Trim home manager generations to 5", "Remove orphan packages": "Remove orphan packages", "Uninstall unused flatpak packages": "Uninstall unused flatpak packages", "<span strikethrough=\"true\">Inaccurate</span> Color picker": "<span strikethrough=\"true\">Inaccurate</span> Color picker", "Result": "Result", "Type to search": "Type to search", "illogical-impulse": "illogical-impulse", "RAM Usage": "RAM Usage", "Swap Usage": "S<PERSON>p <PERSON>", "CPU Usage": "CPU Usage", "Uptime:": "Uptime:", "Screen snip": "Screen snip", "Color picker": "Color picker", "Toggle on-screen keyboard": "Toggle on-screen keyboard", "Night Light": "Night Light", "Color inversion": "Color inversion", "Keep system awake": "Keep system awake", "Cloudflare WARP": "Cloudflare WARP", "Session": "Session", "Bluetooth | Right-click to configure": "Bluetooth | Right-click to configure", "Wifi | Right-click to configure": "Wifi | Right-click to configure", "Right-click to configure": "Right-click to configure", "Unknown": "Unknown", "Reload Environment config": "Reload Environment config", "Open Settings": "Open Settings", "Notifications": "Notifications", "Audio controls": "Audio controls", "Bluetooth": "Bluetooth", "Wifi networks": "Wifi networks", "Quick config": "Quick config", "Silence": "Silence", "Clear": "Clear", "No notifications": "No notifications", "notifications": "notifications", "Close": "Close", "Now": "Now", "Yesterday": "Yesterday", "No audio source": "No audio source", "Remove device": "Remove device", "Connected": "Connected", "Paired": "Paired", "More": "More", "Selected": "Selected", "Connecting to": "Connecting to", "Current network": "Current network", "Authentication": "Authentication", "Authentication failed": "Authentication failed", "Enter network password": "Enter network password", "Properties": "Properties", "Forget": "Forget", "Effects": "Effects", "Transparency": "Transparency", "[AGS]\nMake shell elements transparent\nBlur is also recommended if you enable this": "[AGS]\nMake shell elements transparent\nBlur is also recommended if you enable this", "Blur": "Blur", "[Hyprland]\nEnable blur on transparent elements\nDoesn't affect performance/power consumption unless you have transparent windows.": "[Hyprland]\nEnable blur on transparent elements\nDoesn't affect performance/power consumption unless you have transparent windows.", "X-ray": "X-ray", "[Hyprland]\nMake everything behind a window/layer except the wallpaper not rendered on its blurred surface\nRecommended to improve performance (if you don't abuse transparency/blur) ": "[Hyprland]\nMake everything behind a window/layer except the wallpaper not rendered on its blurred surface\nRecommended to improve performance (if you don't abuse transparency/blur) ", "Size": "Size", "[Hyprland]\nAdjust the blur radius. Generally doesn't affect performance\nHigher = more color spread": "[<PERSON><PERSON><PERSON><PERSON>]\nAdjust the blur radius. Generally doesn't affect performance\nHigher = more color spread", "Passes": "Passes", "[Hyprland] Adjust the number of runs of the blur algorithm\nMore passes = more spread and power consumption\n4 is recommended\n2- would look weird and 6+ would look lame.": "[<PERSON><PERSON><PERSON><PERSON>] Adjust the number of runs of the blur algorithm\nMore passes = more spread and power consumption\n4 is recommended\n2- would look weird and 6+ would look lame.", "Animations": "Animations", "[Hyprland] [GTK]\nEnable animations": "[Hyprland] [GTK]\nEnable animations", "Choreography delay": "Choreography delay", "In milliseconds, the delay between animations of a series": "In milliseconds, the delay between animations of a series", "Developer": "Developer", "Show FPS": "Show FPS", "[Hyprland]\nShow FPS overlay on top-left corner": "[Hyprland]\nShow FPS overlay on top-left corner", "Log to stdout": "Log to stdout", "[Hyprland]\nPrint LOG, ERR, WARN, etc. messages to the console": "[Hyprland]\nPrint LOG, ERR, WARN, etc. messages to the console", "Damage tracking": "Damage tracking", "[Hyprland]\nEnable damage tracking\nGenerally, leave it on.\nTurn off only when a shader doesn't work": "[<PERSON>y<PERSON>rland]\nEnable damage tracking\nGenerally, leave it on.\nTurn off only when a shader doesn't work", "Damage blink": "Damage blink", "[Hyprland] [Epilepsy warning!]\nShow screen damage flashes": "[Hyprland] [Epilepsy warning!]\nShow screen damage flashes", "Not all changes are saved": "Not all changes are saved", "Mo": "Mo", "Tu": "Tu", "We": "We", "Th": "Th", "Fr": "Fr", "Sa": "Sa", "Su": "Su", "Calendar": "Calendar", "To Do": "To Do", "Unfinished": "Unfinished", "Done": "Done", "Finished tasks will go here": "Finished tasks will go here", "Nothing here!": "Nothing here!", "+ New task": "+ New task", "Add a task...": "Add a task...", "Collapse calendar": "Collapse calendar", "Expand calendar": "Expand calendar", "To do tasks": "To do tasks", "Color scheme": "Color scheme", "Options": "Options", "Dark Mode": "Dark Mode", "Ya should go to sleep!": "Ya should go to sleep!", "Theme GTK apps using accent color\n(drawback: dark/light mode switching requires restart)": "Theme GTK apps using accent color\n(drawback: dark/light mode switching requires restart)", "Scheme styles": "Scheme styles", "Vibrant": "Vibrant", "Vibrant+": "Vibrant+", "Expressive": "Expressive", "Monochrome": "Monochrome", "Rainbow": "Rainbow", "Fidelity": "Fidelity", "Fruit Salad": "Fruit Salad", "Tonal Spot": "Tonal Spot", "Content": "Content", "Use arrow keys to navigate.\nEnter to select, Esc to cancel.": "Use arrow keys to navigate.\nEnter to select, Esc to cancel.", "Lock": "Lock", "Logout": "Logout", "Sleep": "Sleep", "Hibernate": "Hibernate", "Shutdown": "Shutdown", "Reboot": "Reboot", "Cancel": "Cancel", "Cheat sheet": "Cheat sheet", "Keybinds": "Keybinds", "Periodic table": "Periodic table", "Essentials for beginners": "Essentials for beginners", "Make shell elements transparent": "Make shell elements transparent", "Actions": "Actions", "Window management": "Window management", "Window arrangement": "Window arrangement", "Workspace management": "Workspace management", "Workspace navigation": "Workspace navigation", "Widgets": "Widgets", "Media": "Media", "Apps": "Apps", "Neutral": "Neutral", "Launch foot (terminal)": "Launch foot (terminal)", "Open app launcher": "Open app launcher", "Change wallpaper": "Change wallpaper", "Clipboard history >> clipboard": "Clipboard history >> clipboard", "Pick emoji >> clipboard": "Pick emoji >> clipboard", "Screen snip >> edit": "Screen snip >> edit", "Screen snip to text >> clipboard": "Screen snip to text >> clipboard", "Pick color (Hex) >> clipboard": "Pick color (Hex) >> clipboard", "Screenshot >> clipboard": "Screenshot >> clipboard", "Screenshot >> clipboard & file": "Screenshot >> clipboard & file", "Record region (no sound)": "Record region (no sound)", "Record screen (with sound)": "Record screen (with sound)", "Suspend system": "Suspend system", "Move focus in direction": "Move focus in direction", "Move window": "Move window", "Resize window": "Resize window", "Close window": "Close window", "Pick and kill a window": "Pick and kill a window", "Window: move in direction": "Window: move in direction", "Window: split ratio +/- 0.1": "Window: split ratio +/- 0.1", "Float/unfloat window": "Float/unfloat window", "Toggle fake fullscreen": "Toggle fake fullscreen", "Toggle fullscreen": "Toggle fullscreen", "Toggle maximization": "Toggle maximization", "Focus workspace # (1, 2, 3, 4, ...)": "Focus workspace # (1, 2, 3, 4, ...)", "Workspace: focus left/right": "Workspace: focus left/right", "Workspace: toggle special": "Workspace: toggle special", "Window: move to workspace # (1, 2, 3, 4, ...)": "Window: move to workspace # (1, 2, 3, 4, ...)", "Window: move to workspace left/right": "Window: move to workspace left/right", "Window: move to workspace special": "Window: move to workspace special", "Window: pin (show on all workspaces)": "Window: pin (show on all workspaces)", "Restart widgets": "Restart widgets", "Cycle bar mode (normal, focus)": "Cycle bar mode (normal, focus)", "Toggle overview/launcher": "Toggle overview/launcher", "Show cheatsheet": "Show cheatsheet", "Toggle left sidebar": "Toggle left sidebar", "Toggle right sidebar": "Toggle right sidebar", "Toggle music controls": "Toggle music controls", "View color scheme and options": "View color scheme and options", "Toggle power menu": "Toggle power menu", "Toggle crosshair": "Toggle crosshair", "Next track": "Next track", "Previous track": "Previous track", "Play/pause media": "Play/pause media", "Launch Zed (editor)": "<PERSON> Zed (editor)", "Launch VSCode (editor)": "Launch VSCode (editor)", "Launch Nautilus (file manager)": "Launch Nautilus (file manager)", "Launch Firefox (browser)": "Launch Firefox (browser)", "Launch GNOME Text Editor": "Launch GNOME Text Editor", "Launch WPS Office": "Launch WPS Office", "Launch GNOME Settings": "Launch GNOME Settings", "Launch pavucontrol (volume mixer)": "Launch pavucontrol (volume mixer)", "Launch EasyEffects (equalizer & other audio effects)": "Launch EasyEffects (equalizer & other audio effects)", "Launch GNOME System monitor": "Launch GNOME System monitor", "Toggle fallback launcher: anyrun": "Toggle fallback launcher: anyrun", "Toggle fallback launcher: fuzzel": "Toggle fallback launcher: fuzzel", "Initialization complete!": "Initialization complete!", "Not found": "Not found:", "Calling API": "Calling API", "Downloading image": "Downloading image", "Finished!": "Finished!", "Error": "Error", "Not found!": "Not found!", "Go to file url": "Go to file url", "Save image": "Save image", "Hoard": "Hoard", "Open externally": "Open externally", "You are an assistant on a sidebar of a Wayland Linux desktop. Please always use a casual tone when answering your questions, unless requested otherwise or making writing suggestions. These are the steps you should take to respond to the user's queries:\n1. If it's a writing- or grammar-related question or a sentence in quotation marks, Please point out errors and correct when necessary using underlines, and make the writing more natural where appropriate without making too major changes. If you're given a sentence in quotes but is grammatically correct, explain briefly concepts that are uncommon.\n2. If it's a question about system tasks, give a bash command in a code block with brief explanation.\n3. Otherwise, when asked to summarize information or explaining concepts, you are should use bullet points and headings. For mathematics expressions, you *have to* use LaTeX within a code block with the language set as \"latex\". \nNote: Use casual language, be short, while ensuring the factual correctness of your response. If you are unsure or don’t have enough information to provide a confident answer, simply say “I don’t know” or “I’m not sure.”. \nThanks!": "You are an assistant on a sidebar of a Wayland Linux desktop. Please always use a casual tone when answering your questions, unless requested otherwise or making writing suggestions. These are the steps you should take to respond to the user's queries:\n1. If it's a writing- or grammar-related question or a sentence in quotation marks, Please point out errors and correct when necessary using underlines, and make the writing more natural where appropriate without making too major changes. If you're given a sentence in quotes but is grammatically correct, explain briefly concepts that are uncommon.\n2. If it's a question about system tasks, give a bash command in a code block with brief explanation.\n3. Otherwise, when asked to summarize information or explaining concepts, you are should use bullet points and headings. For mathematics expressions, you *have to* use LaTeX within a code block with the language set as \"latex\". \nNote: Use casual language, be short, while ensuring the factual correctness of your response. If you are unsure or don’t have enough information to provide a confident answer, simply say “I don’t know” or “I’m not sure.”. \nThanks!", "Feels like": "Feels like"}